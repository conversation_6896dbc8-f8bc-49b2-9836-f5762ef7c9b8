import Head from 'next/head';
import Link from 'next/link';
import React from 'react';

const HomePage: React.FC = () => {
  return (
    <>
      <Head>
        <title>DealClosed Partner - The platform where sales come together</title>
        <meta name="description" content="At DealClosed, we connect businesses with freelance sales agents in a win-win situation. Businesses gain access to top sales agents, while agents have the freedom to work flexibly—anytime, anywhere." />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      {/* Navigation - Fixed sticky navbar */}
      <nav className="navbar navbar-expand-lg navbar-light bg-white shadow-sm py-3 fixed-top">
        <div className="container">
          <Link href="/" className="navbar-brand d-flex align-items-center">
            <img src="/assets/img/default.png" alt="DealClosed Logo" width="40" height="40" className="me-2" />
            <span className="fw-bold text-success" style={{ fontSize: '1.7rem' }}>DealClosed</span>
          </Link>

          <button
            className="navbar-toggler"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#navbarNav"
          >
            <span className="navbar-toggler-icon"></span>
          </button>

          <div className="collapse navbar-collapse" id="navbarNav">
            <ul className="navbar-nav me-auto ms-4">
              <li className="nav-item">
                <Link href="/" className="nav-link text-success" style={{ fontWeight: '500', fontSize: '1.05rem' }}>At home</Link>
              </li>
              <li className="nav-item">
                <Link href="/assignments" className="nav-link text-dark" style={{ fontWeight: '500', fontSize: '1.05rem' }}>Commands</Link>
              </li>
              <li className="nav-item">
                <Link href="/freelancers" className="nav-link text-dark" style={{ fontWeight: '500', fontSize: '1.05rem' }}>Discover freelancers</Link>
              </li>
              <li className="nav-item">
                <Link href="/companies" className="nav-link text-dark" style={{ fontWeight: '500', fontSize: '1.05rem' }}>Find company</Link>
              </li>
              <li className="nav-item">
                <Link href="/courses" className="nav-link text-dark" style={{ fontWeight: '500', fontSize: '1.05rem' }}>Courses</Link>
              </li>
              <li className="nav-item">
                <Link href="/ranking" className="nav-link text-dark" style={{ fontWeight: '500', fontSize: '1.05rem' }}>Rankings</Link>
              </li>
              <li className="nav-item">
                <Link href="/blog" className="nav-link text-dark" style={{ fontWeight: '500', fontSize: '1.05rem' }}>Blogs</Link>
              </li>
            </ul>

            <div className="d-flex gap-2 align-items-center">
              <Link href="/login" className="nav-link text-dark" style={{ fontWeight: '500', fontSize: '1.05rem' }}>
                Login
              </Link>
              <Link href="/register" className="btn btn-success rounded-pill px-4 py-2" style={{ fontWeight: '500', fontSize: '15px' }}>
                Get started
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <section className="position-relative" style={{
        backgroundImage: 'url(/assets/img/default.png)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        minHeight: '100vh',
        marginTop: '80px'
      }}>
        <div className="container h-100 d-flex align-items-center">
          <div className="row w-100">
            <div className="col-lg-8 text-white">
              <h1 className="display-3 fw-bold mb-4" style={{ fontSize: '3.5rem', lineHeight: '1.2' }}>
                Welcome to Deal Closed,<br />
                <span style={{ fontSize: '2.8rem' }}>the platform where sales come together</span>
              </h1>
              <p className="lead mb-4" style={{ fontSize: '1.25rem', opacity: '0.95', maxWidth: '600px' }}>
                At DealClosed, we connect companies with freelance sales agents in a win-win situation. Businesses get access to
                top sales agents, while agents have the freedom to work flexibly — anytime, anywhere.
              </p>
              <button
                className="btn btn-lg px-5 py-3 rounded-pill"
                style={{
                  backgroundColor: 'transparent',
                  border: '2px solid white',
                  color: 'white',
                  fontSize: '16px',
                  fontWeight: '400',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  const target = e.target as HTMLButtonElement;
                  target.style.backgroundColor = '#28a745';
                  target.style.borderColor = '#28a745';
                }}
                onMouseLeave={(e) => {
                  const target = e.target as HTMLButtonElement;
                  target.style.backgroundColor = 'transparent';
                  target.style.borderColor = 'white';
                }}
                onClick={() => window.location.href = '/register'}
              >
                Get started
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Connecting Companies Section */}
      <section className="py-3" style={{ backgroundColor: '#f8f9fa' }}>
        <div className="container text-center">
          <h6 className="mb-0 text-muted" style={{ fontSize: '0.9rem' }}>Connecting companies with freelance sales associates</h6>
        </div>
      </section>

      {/* Main Description Section */}
      <section className="py-5" style={{ backgroundColor: '#f8f9fa' }}>
        <div className="container">
          <div className="row">
            <div className="col-lg-12">
              <h2 className="fw-bold mb-4" style={{ fontSize: '2.5rem', color: '#333' }}>
                Connecting companies with freelance sales agents who deliver.
              </h2>
              <p className="mb-3" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>
                <strong>DealClosed</strong> is the platform where companies and freelance sales agents meet.
                No expensive contracts or recruitment agencies – just collaboration based on performance.
              </p>
              <p className="mb-3" style={{ fontSize: '1.1rem', lineHeight: '1.6' }}>
                Whether you're looking to close deals or find someone who can do it for you,
                DealClosed is the place to be.
              </p>
              <p style={{ fontSize: '1.1rem', lineHeight: '1.6', color: '#666', fontStyle: 'italic' }}>
                Work flexibly. Earn fairly. Grow together.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section - Exact match to your design */}
      <section className="py-5 bg-light">
        <div className="container">
          <div className="row g-4">
            <div className="col-lg-6">
              <div className="card h-100 border-0 shadow-sm position-relative" style={{ borderRadius: '20px', minHeight: '350px' }}>
                <div
                  className="position-absolute top-0 start-0 h-100"
                  style={{
                    width: '8px',
                    backgroundColor: '#28a745',
                    borderTopLeftRadius: '20px',
                    borderBottomLeftRadius: '20px'
                  }}
                ></div>
                <div className="card-body p-5" style={{ paddingLeft: '3rem' }}>
                  <div className="text-center mb-4">
                    <div className="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style={{ width: '80px', height: '80px' }}>
                      <svg width="40" height="40" fill="#28a745" viewBox="0 0 16 16">
                        <path d="M7 14s-1 0-1-1 1-4 5-4 5 3 5 4-1 1-1 1H7zm4-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>
                        <path fillRule="evenodd" d="M5.216 14A2.238 2.238 0 0 1 5 13c0-1.355.68-2.75 1.936-3.72A6.325 6.325 0 0 0 5 9c-4 0-5 3-5 4s1 1 1 1h4.216z"/>
                        <path d="M4.5 8a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5z"/>
                      </svg>
                    </div>
                  </div>
                  <h4 className="text-center fw-bold mb-4" style={{ fontSize: '1.4rem', color: '#666' }}>Benefits for freelance sales associates</h4>
                  <ul className="list-unstyled" style={{ fontSize: '1rem', lineHeight: '1.8' }}>
                    <li className="mb-3">
                      • <strong>Work wherever you want</strong>—at home, in a café, or on the go—you
                      choose when and where you work.
                    </li>
                    <li className="mb-3">
                      • <strong>Earn attractive commissions:</strong> Get a fair fee per deal closed. Your
                      success is in your hands.
                    </li>
                    <li className="mb-3">
                      • <strong>Diverse opportunities:</strong> discover a wide range of projects and
                      companies that match your skills and interests
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <div className="col-lg-6">
              <div className="card h-100 border-0 shadow-sm position-relative" style={{ borderRadius: '20px', minHeight: '350px' }}>
                <div
                  className="position-absolute top-0 start-0 h-100"
                  style={{
                    width: '8px',
                    backgroundColor: '#28a745',
                    borderTopLeftRadius: '20px',
                    borderBottomLeftRadius: '20px'
                  }}
                ></div>
                <div className="card-body p-5" style={{ paddingLeft: '3rem' }}>
                  <div className="text-center mb-4">
                    <div className="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style={{ width: '80px', height: '80px' }}>
                      <svg width="40" height="40" fill="#28a745" viewBox="0 0 16 16">
                        <path d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z"/>
                        <path d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319z"/>
                      </svg>
                    </div>
                  </div>
                  <h4 className="text-center fw-bold mb-4" style={{ fontSize: '1.4rem', color: '#666' }}>Benefits for companies</h4>
                  <ul className="list-unstyled" style={{ fontSize: '1rem', lineHeight: '1.8' }}>
                    <li className="mb-3">
                      • <strong>More revenue, less risk</strong>—only pay for results. No fixed salaries or
                      recruitment costs – just a clear commission per deal closed.
                    </li>
                    <li className="mb-3">
                      • <strong>Access to skilled sales reps:</strong> Connect with motivated freelancers who
                      are ready to grow your business.
                    </li>
                    <li className="mb-3">
                      • <strong>Quick and easy collaboration:</strong> Post your brief, select the right sales
                      agent, and let the deals flow.
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why We Do What We Do Section */}
      <section className="py-5 bg-white">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-6">
              <h2 className="fw-bold mb-4" style={{ fontSize: '2.5rem', color: '#333' }}>Why we do what we do</h2>
              <p className="text-muted mb-4" style={{ fontSize: '1rem', lineHeight: '1.6', fontStyle: 'italic' }}>
                At DealClosed, we believe in solving real sales challenges with simple, effective solutions.
                Our platform has been developed in response to a clear need in the market:
              </p>

              <div className="mb-4">
                <div className="d-flex align-items-start mb-3">
                  <div className="me-3">
                    <div className="bg-success rounded-circle d-flex align-items-center justify-content-center" style={{ width: '24px', height: '24px', minWidth: '24px' }}>
                      <svg width="14" height="14" fill="white" viewBox="0 0 16 16">
                        <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                      </svg>
                    </div>
                  </div>
                  <div>
                    <h5 className="mb-2" style={{ fontSize: '1.1rem', fontWeight: '600' }}>For businesses:</h5>
                    <p style={{ fontSize: '0.95rem', lineHeight: '1.6', margin: '0' }}>
                      Many companies struggle to find reliable sales talent without long-term contracts or high hiring costs.
                      They need flexible, results-driven solutions to grow their revenue risk-free.
                    </p>
                  </div>
                </div>
              </div>

              <div className="mb-4">
                <div className="d-flex align-items-start mb-3">
                  <div className="me-3">
                    <div className="bg-success rounded-circle d-flex align-items-center justify-content-center" style={{ width: '24px', height: '24px', minWidth: '24px' }}>
                      <svg width="14" height="14" fill="white" viewBox="0 0 16 16">
                        <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                      </svg>
                    </div>
                  </div>
                  <div>
                    <h5 className="mb-2" style={{ fontSize: '1.1rem', fontWeight: '600' }}>For sales agents:</h5>
                    <p style={{ fontSize: '0.95rem', lineHeight: '1.6', margin: '0' }}>
                      Talented people often lack the freedom to work on their own terms or find opportunities that match their unique skill sets.
                      Many are stuck in rigid working models that limit their potential. We give them the opportunity to work freely,
                      wherever they are, and achieve success based on performance.
                    </p>
                  </div>
                </div>
              </div>

              <button
                className="btn btn-success rounded-pill px-4 py-2"
                style={{ fontSize: '0.9rem', fontWeight: '400' }}
                onClick={() => window.location.href = '/about'}
              >
                Read more →
              </button>
            </div>
            <div className="col-lg-6">
              <img
                src="/assets/img/default.png"
                alt="Why we do what we do"
                className="img-fluid rounded shadow"
                style={{ width: '100%', height: '400px', objectFit: 'cover' }}
              />
            </div>
          </div>
        </div>
      </section>


      {/* Why choose us section - Exact match to your design */}
      <section className="py-5">
        <div className="container">
          <div className="row g-4">
            {/* Green card - Why choose us - Square shape */}
            <div className="col-lg-4">
              <div className="card h-100 border-0 shadow-sm text-white" style={{ backgroundColor: '#28a745', borderRadius: '20px', minHeight: '400px', aspectRatio: '1' }}>
                <div className="card-body p-4 d-flex flex-column justify-content-center">
                  <h3 className="fw-bold mb-4" style={{ fontSize: '1.6rem' }}>Why should you choose us?</h3>
                  <p className="mb-4" style={{ fontSize: '0.9rem', lineHeight: '1.6' }}>
                    To bridge this gap, we founded DealClosed. By linking companies with motivated freelance
                    sales staff, we offer a win-win situation: companies achieve results without unnecessary
                    overhead and sales employees get the flexibility to work how, when and where they want.
                  </p>
                  <button
                    className="btn px-4 py-2 rounded-pill"
                    style={{
                      backgroundColor: '#1e7e34',
                      border: 'none',
                      color: 'white',
                      fontSize: '0.9rem',
                      fontWeight: '400',
                      transition: 'all 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      const target = e.target as HTMLButtonElement;
                      target.style.backgroundColor = 'white';
                      target.style.color = '#28a745';
                    }}
                    onMouseLeave={(e) => {
                      const target = e.target as HTMLButtonElement;
                      target.style.backgroundColor = '#1e7e34';
                      target.style.color = 'white';
                    }}
                    onClick={() => window.location.href = '/about'}
                  >
                    Learn more →
                  </button>
                </div>
              </div>
            </div>

            {/* Transparency - Increased width */}
            <div className="col-lg-3">
              <div className="card h-100 border-0 shadow-sm position-relative" style={{ borderRadius: '20px', minHeight: '400px' }}>
                <div
                  className="position-absolute top-0 start-0 h-100"
                  style={{
                    width: '8px',
                    backgroundColor: '#28a745',
                    borderTopLeftRadius: '20px',
                    borderBottomLeftRadius: '20px'
                  }}
                ></div>
                <div className="card-body p-4 text-center d-flex flex-column justify-content-center">
                  <div className="mb-4">
                    <div className="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style={{ width: '60px', height: '60px' }}>
                      <svg width="30" height="30" fill="#28a745" viewBox="0 0 16 16">
                        <path d="M8 1a2.5 2.5 0 0 1 2.5 2.5V4h-5v-.5A2.5 2.5 0 0 1 8 1zm3.5 3v-.5a3.5 3.5 0 1 0-7 0V4H1v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4h-3.5zM2 5h12v9a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V5z"/>
                      </svg>
                    </div>
                  </div>
                  <h5 className="fw-bold mb-3" style={{ fontSize: '1.2rem' }}>Transparency</h5>
                  <p className="text-muted" style={{ fontSize: '0.9rem', lineHeight: '1.5' }}>
                    With a simple 15% commission on successful deals, we guarantee fairness
                    and clarity in every transaction.
                  </p>
                </div>
              </div>
            </div>

            {/* Flexibility - Increased width */}
            <div className="col-lg-3">
              <div className="card h-100 border-0 shadow-sm position-relative" style={{ borderRadius: '20px', minHeight: '400px' }}>
                <div
                  className="position-absolute top-0 start-0 h-100"
                  style={{
                    width: '8px',
                    backgroundColor: '#28a745',
                    borderTopLeftRadius: '20px',
                    borderBottomLeftRadius: '20px'
                  }}
                ></div>
                <div className="card-body p-4 text-center d-flex flex-column justify-content-center">
                  <div className="mb-4">
                    <div className="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style={{ width: '60px', height: '60px' }}>
                      <svg width="30" height="30" fill="#28a745" viewBox="0 0 16 16">
                        <path d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z"/>
                        <path d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319z"/>
                      </svg>
                    </div>
                  </div>
                  <h5 className="fw-bold mb-3" style={{ fontSize: '1.2rem' }}>Flexibility</h5>
                  <p className="text-muted" style={{ fontSize: '0.9rem', lineHeight: '1.5' }}>
                    We embrace the future of work and offer sellers and
                    companies freedom and choice.
                  </p>
                </div>
              </div>
            </div>

            {/* Focus on results - Increased width */}
            <div className="col-lg-2">
              <div className="card h-100 border-0 shadow-sm position-relative" style={{ borderRadius: '20px', minHeight: '400px' }}>
                <div
                  className="position-absolute top-0 start-0 h-100"
                  style={{
                    width: '8px',
                    backgroundColor: '#28a745',
                    borderTopLeftRadius: '20px',
                    borderBottomLeftRadius: '20px'
                  }}
                ></div>
                <div className="card-body p-4 text-center d-flex flex-column justify-content-center">
                  <div className="mb-4">
                    <div className="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style={{ width: '60px', height: '60px' }}>
                      <svg width="30" height="30" fill="#28a745" viewBox="0 0 16 16">
                        <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8.5 4.5a.5.5 0 0 0-1 0v3h-3a.5.5 0 0 0 0 1h3v3a.5.5 0 0 0 1 0v-3h3a.5.5 0 0 0 0-1h-3v-3z"/>
                      </svg>
                    </div>
                  </div>
                  <h5 className="fw-bold mb-3" style={{ fontSize: '1.2rem' }}>Focus on results</h5>
                  <p className="text-muted" style={{ fontSize: '0.9rem', lineHeight: '1.5' }}>
                    We believe in rewarding results, not effort. We create
                    a performance-oriented culture for everyone
                    involved.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Assignments Section */}
      <section className="py-5 bg-light">
        <div className="container">
          <h2 className="fw-bold mb-4">Assignments</h2>
          <h5 className="text-muted mb-4">Latest Assignments</h5>

          <div className="row">
            <div className="col-lg-4">
              <div className="card border-0 shadow-sm">
                <div className="card-img-top bg-primary d-flex align-items-center justify-content-center" style={{ height: '200px' }}>
                  <div className="text-center text-white">
                    <svg width="60" height="60" fill="white" viewBox="0 0 16 16">
                      <path d="M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854V1.146zM1.175 1a.146.146 0 0 0-.146.146v13.708c0 .08.066.146.146.146h13.65a.146.146 0 0 0 .146-.146V1.146a.146.146 0 0 0-.146-.146H1.175z"/>
                      <path d="M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z"/>
                    </svg>
                    <h6 className="mt-2 mb-0">Information Technology</h6>
                  </div>
                </div>
                <div className="card-body">
                  <span className="badge bg-primary mb-2">Information Technology</span>
                  <span className="text-success fs-4 fw-bold">$</span>
                  <h5 className="fw-bold mb-2">
                    <Link href="/assignments/5" className="text-decoration-none text-dark">
                      Web application development
                    </Link>
                  </h5>
                  <p className="text-muted mb-2">Test Assignment</p>
                  <p className="mb-2"><strong>Payment Type:</strong> Hourly ($17.00)</p>
                  <p className="mb-3">Applications: 1</p>
                  <Link href="/login" className="btn btn-success w-100">
                    Take This Deal
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer - Exact match to your design */}
      <footer className="bg-white py-5 border-top">
        <div className="container">
          <div className="row">
            <div className="col-lg-3">
              <h5 className="fw-bold text-success mb-4">DealClosed</h5>
              <p className="text-muted mb-2">Amsterdam, The Netherlands</p>
              <p className="text-muted mb-4"><strong>Email:</strong> <EMAIL></p>
            </div>

            <div className="col-lg-3">
              <h6 className="fw-bold mb-3 text-success">Useful links</h6>
              <ul className="list-unstyled">
                <li className="mb-2"><Link href="/" className="text-muted text-decoration-none">Home</Link></li>
                <li className="mb-2"><Link href="/about" className="text-muted text-decoration-none">About us</Link></li>
                <li className="mb-2"><Link href="/assignments" className="text-muted text-decoration-none">Assignments</Link></li>
              </ul>
            </div>

            <div className="col-lg-3">
              <h6 className="fw-bold mb-3 text-success">Other links</h6>
              <ul className="list-unstyled">
                <li className="mb-2"><Link href="/contact" className="text-muted text-decoration-none">Contact Us</Link></li>
                <li className="mb-2"><Link href="/terms" className="text-muted text-decoration-none">Terms of service</Link></li>
                <li className="mb-2"><Link href="/privacy" className="text-muted text-decoration-none">Privacy policy</Link></li>
              </ul>
            </div>

            <div className="col-lg-3">
              <h6 className="fw-bold mb-3 text-success">Follow us</h6>
              <div className="d-flex gap-2">
                <a href="#" className="btn btn-outline-secondary btn-sm rounded-circle">
                  <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z"/>
                  </svg>
                </a>
                <a href="#" className="btn btn-outline-secondary btn-sm rounded-circle">
                  <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z"/>
                  </svg>
                </a>
                <a href="#" className="btn btn-outline-secondary btn-sm rounded-circle">
                  <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"/>
                  </svg>
                </a>
                <a href="#" className="btn btn-outline-secondary btn-sm rounded-circle">
                  <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854V1.146zm4.943 12.248V6.169H2.542v7.225h2.401zm-1.2-8.212c.837 0 1.358-.554 1.358-1.248-.015-.709-.52-1.248-1.342-1.248-.822 0-1.359.54-1.359 1.248 0 .694.521 1.248 1.327 1.248h.016zm4.908 8.212V9.359c0-.216.016-.432.08-.586.173-.431.568-.878 1.232-.878.869 0 1.216.662 1.216 1.634v3.865h2.401V9.25c0-2.22-1.184-3.252-2.764-3.252-1.274 0-1.845.7-2.165 1.193v.025h-.016a5.54 5.54 0 0 1 .016-.025V6.169h-2.4c.03.678 0 7.225 0 7.225h2.4z"/>
                  </svg>
                </a>
              </div>
            </div>
          </div>

          <hr className="my-4" />

          <div className="row align-items-center">
            <div className="col-md-6">
              <p className="text-muted mb-0">© Copyright <strong>DealClosed,</strong> All Rights Reserved</p>
            </div>
            <div className="col-md-6 text-end">
              <img src="/us.png" alt="English" width="20" className="me-2" />
              <img src="/nl.png" alt="Dutch" width="20" />
            </div>
          </div>
        </div>
      </footer>
    </>
  );
};

export default HomePage;
