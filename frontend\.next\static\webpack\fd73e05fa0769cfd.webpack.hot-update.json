{"c": ["pages/index", "webpack"], "r": ["pages/dashboard", "pages/verify-email"], "m": ["./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CFAST%5CFreelance%5CProject9_SalesPlatform%5CDealClosed%5Cfrontend%5Csrc%5Cpages%5Cdashboard.tsx&page=%2Fdashboard!", "./node_modules/react-icons/fi/index.esm.js", "./node_modules/react-icons/lib/esm/iconBase.js", "./node_modules/react-icons/lib/esm/iconContext.js", "./node_modules/react-icons/lib/esm/iconsManifest.js", "./node_modules/react-icons/lib/esm/index.js", "./src/components/Layout.tsx", "./src/pages/dashboard.tsx", "__barrel_optimize__?names=FiAlertCircle,FiCheckCircle,FiEdit,FiMail,FiUser!=!./node_modules/react-icons/fi/index.esm.js", "__barrel_optimize__?names=FiLogOut,FiMail,FiSettings,FiUser!=!./node_modules/react-icons/fi/index.esm.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CFAST%5CFreelance%5CProject9_SalesPlatform%5CDealClosed%5Cfrontend%5Csrc%5Cpages%5Cverify-email.tsx&page=%2Fverify-email!", "./src/pages/verify-email.tsx", "__barrel_optimize__?names=FiCheckCircle,FiMail,FiXCircle!=!./node_modules/react-icons/fi/index.esm.js"]}