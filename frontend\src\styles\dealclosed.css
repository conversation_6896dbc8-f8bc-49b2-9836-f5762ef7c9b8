/* DealClosed Custom Styles - Exact match to dealclosedpartner.nl */

/* Import Google Fonts to match the website */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Global font family */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-weight: 400;
  line-height: 1.6;
  color: #333;
}

/* Custom Bootstrap color overrides */
:root {
  --bs-success: #28a745;
  --bs-success-rgb: 40, 167, 69;
  --bs-primary: #007bff;
  --bs-primary-rgb: 0, 123, 255;
}

/* Navigation styling */
.navbar-brand img {
  transition: transform 0.2s ease;
}

.navbar-brand:hover img {
  transform: scale(1.05);
}

.nav-link {
  font-weight: 500;
  transition: color 0.2s ease;
}

.nav-link:hover {
  color: var(--bs-success) !important;
}

/* Button styling to match website */
.btn {
  font-weight: 600;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.btn-success {
  background-color: var(--bs-success);
  border-color: var(--bs-success);
}

.btn-success:hover {
  background-color: #218838;
  border-color: #1e7e34;
  transform: translateY(-1px);
}

.btn-outline-primary {
  color: var(--bs-primary);
  border-color: var(--bs-primary);
}

.btn-outline-primary:hover {
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
  transform: translateY(-1px);
}

/* Hero section styling */
.hero-section {
  background: linear-gradient(135deg, rgba(0,0,0,0.7), rgba(0,0,0,0.5));
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
}

/* Card styling */
.card {
  border: none;
  border-radius: 12px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Form styling */
.form-control {
  border-radius: 8px;
  border: 1px solid #ddd;
  padding: 12px 16px;
  font-size: 14px;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus {
  border-color: var(--bs-success);
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-label {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  color: #333;
}

.display-3, .display-4 {
  font-weight: 800;
}

.lead {
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 1.6;
}

/* Footer styling */
footer {
  background-color: #fff;
  border-top: 1px solid #e9ecef;
}

footer h5, footer h6 {
  color: var(--bs-success);
}

footer .text-muted {
  color: #6c757d !important;
}

/* Social icons */
.btn-outline-secondary {
  border-color: #ddd;
  color: #6c757d;
}

.btn-outline-secondary:hover {
  background-color: var(--bs-success);
  border-color: var(--bs-success);
  color: white;
}

/* Assignment card styling */
.assignment-card {
  border-left: 4px solid var(--bs-success);
}

.assignment-card .badge {
  font-size: 0.75rem;
  font-weight: 600;
}

/* Benefits section styling */
.benefits-icon {
  width: 60px;
  height: 60px;
  background: rgba(40, 167, 69, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Why choose us section */
.why-choose-card {
  background: linear-gradient(135deg, var(--bs-success), #20c997);
  color: white;
}

.why-choose-card .btn-outline-light {
  border-color: rgba(255,255,255,0.5);
  color: white;
}

.why-choose-card .btn-outline-light:hover {
  background-color: white;
  color: var(--bs-success);
}

/* Feature cards */
.feature-card {
  border-left: 4px solid var(--bs-success);
  height: 100%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .display-3, .display-4 {
    font-size: 2.5rem;
  }
  
  .hero-section {
    background-attachment: scroll;
  }
  
  .navbar-nav {
    text-align: center;
  }
  
  .btn {
    width: 100%;
    margin-bottom: 10px;
  }
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading states */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--bs-success);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #218838;
}

/* Text selection */
::selection {
  background-color: var(--bs-success);
  color: white;
}

/* Focus states for accessibility */
.btn:focus,
.form-control:focus,
.nav-link:focus {
  outline: 2px solid var(--bs-success);
  outline-offset: 2px;
}

/* Custom spacing */
.section-padding {
  padding: 80px 0;
}

.section-padding-sm {
  padding: 60px 0;
}

/* Brand colors */
.text-brand {
  color: var(--bs-success) !important;
}

.bg-brand {
  background-color: var(--bs-success) !important;
}

.border-brand {
  border-color: var(--bs-success) !important;
}
