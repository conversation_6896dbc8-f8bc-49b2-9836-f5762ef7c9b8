"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst HomePage = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"DealClosed Partner - The platform where sales come together\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"At DealClosed, we connect businesses with freelance sales agents in a win-win situation. Businesses gain access to top sales agents, while agents have the freedom to work flexibly—anytime, anywhere.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"navbar navbar-expand-lg navbar-light bg-white shadow-sm py-3 fixed-top\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"navbar-brand d-flex align-items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"me-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"32\",\n                                        height: \"32\",\n                                        viewBox: \"0 0 100 100\",\n                                        fill: \"none\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"50\",\n                                                cy: \"50\",\n                                                r: \"45\",\n                                                fill: \"#28a745\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 21,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M30 50L45 65L70 35\",\n                                                stroke: \"white\",\n                                                strokeWidth: \"6\",\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 22,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"fw-bold text-success\",\n                                    style: {\n                                        fontSize: \"1.7rem\"\n                                    },\n                                    children: \"DealClosed\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"navbar-toggler\",\n                            type: \"button\",\n                            \"data-bs-toggle\": \"collapse\",\n                            \"data-bs-target\": \"#navbarNav\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"navbar-toggler-icon\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"collapse navbar-collapse\",\n                            id: \"navbarNav\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"navbar-nav me-auto ms-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\",\n                                                className: \"nav-link text-success\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"At home\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 40,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/assignments\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Commands\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 43,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/freelancers\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Discover freelancers\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/companies\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Find company\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/courses\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Courses\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/ranking\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Rankings\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/blog\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Blogs\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"d-flex gap-2 align-items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/login\",\n                                            className: \"nav-link text-dark\",\n                                            style: {\n                                                fontWeight: \"500\",\n                                                fontSize: \"1.05rem\"\n                                            },\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/register\",\n                                            className: \"btn btn-success rounded-pill px-4 py-2\",\n                                            style: {\n                                                fontWeight: \"500\",\n                                                fontSize: \"15px\"\n                                            },\n                                            children: \"Get started\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"position-relative\",\n                style: {\n                    backgroundImage: \"url(/hero-bg.jpg)\",\n                    backgroundSize: \"cover\",\n                    backgroundPosition: \"center\",\n                    minHeight: \"100vh\",\n                    marginTop: \"80px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container h-100 d-flex align-items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row w-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-lg-8 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"display-3 fw-bold mb-4\",\n                                    style: {\n                                        fontSize: \"3.5rem\",\n                                        lineHeight: \"1.2\"\n                                    },\n                                    children: [\n                                        \"Welcome to Deal Closed,\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 40\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"2.8rem\"\n                                            },\n                                            children: \"the platform where sales come together\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"lead mb-4\",\n                                    style: {\n                                        fontSize: \"1.25rem\",\n                                        opacity: \"0.95\",\n                                        maxWidth: \"600px\"\n                                    },\n                                    children: \"At DealClosed, we connect companies with freelance sales agents in a win-win situation. Businesses get access to top sales agents, while agents have the freedom to work flexibly — anytime, anywhere.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn btn-lg px-5 py-3 rounded-pill\",\n                                    style: {\n                                        backgroundColor: \"transparent\",\n                                        border: \"2px solid white\",\n                                        color: \"white\",\n                                        fontSize: \"16px\",\n                                        fontWeight: \"400\",\n                                        transition: \"all 0.3s ease\"\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        const target = e.target;\n                                        target.style.backgroundColor = \"#28a745\";\n                                        target.style.borderColor = \"#28a745\";\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        const target = e.target;\n                                        target.style.backgroundColor = \"transparent\";\n                                        target.style.borderColor = \"white\";\n                                    },\n                                    onClick: ()=>window.location.href = \"/register\",\n                                    children: \"Get started\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-3\",\n                style: {\n                    backgroundColor: \"#f8f9fa\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                        className: \"mb-0 text-muted\",\n                        style: {\n                            fontSize: \"0.9rem\"\n                        },\n                        children: \"Connecting companies with freelance sales associates\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-lg-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"fw-bold mb-4\",\n                                    style: {\n                                        fontSize: \"2.5rem\",\n                                        color: \"#333\"\n                                    },\n                                    children: \"Connecting companies with freelance sales agents who deliver.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-3\",\n                                    style: {\n                                        fontSize: \"1.1rem\",\n                                        lineHeight: \"1.6\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"DealClosed\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" is the platform where companies and freelance sales agents meet. No expensive contracts or recruitment agencies – just collaboration based on performance.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-3\",\n                                    style: {\n                                        fontSize: \"1.1rem\",\n                                        lineHeight: \"1.6\"\n                                    },\n                                    children: \"Whether you're looking to close deals or find someone who can do it for you, DealClosed is the place to be.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        fontSize: \"1.1rem\",\n                                        lineHeight: \"1.6\",\n                                        color: \"#666\",\n                                        fontStyle: \"italic\"\n                                    },\n                                    children: \"Work flexibly. Earn fairly. Grow together.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5 bg-light\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row g-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"20px\",\n                                        minHeight: \"350px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"8px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"20px\",\n                                                borderBottomLeftRadius: \"20px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-5\",\n                                            style: {\n                                                paddingLeft: \"3rem\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center\",\n                                                        style: {\n                                                            width: \"80px\",\n                                                            height: \"80px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"40\",\n                                                            height: \"40\",\n                                                            fill: \"#28a745\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M7 14s-1 0-1-1 1-4 5-4 5 3 5 4-1 1-1 1H7zm4-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M5.216 14A2.238 2.238 0 0 1 5 13c0-1.355.68-2.75 1.936-3.72A6.325 6.325 0 0 0 5 9c-4 0-5 3-5 4s1 1 1 1h4.216z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M4.5 8a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-center fw-bold mb-4\",\n                                                    style: {\n                                                        fontSize: \"1.4rem\",\n                                                        color: \"#666\"\n                                                    },\n                                                    children: \"Benefits for freelance sales associates\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-unstyled\",\n                                                    style: {\n                                                        fontSize: \"1rem\",\n                                                        lineHeight: \"1.8\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Work wherever you want\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 180,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \"—at home, in a caf\\xe9, or on the go—you choose when and where you work.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Earn attractive commissions:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 184,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" Get a fair fee per deal closed. Your success is in your hands.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Diverse opportunities:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 188,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" discover a wide range of projects and companies that match your skills and interests\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"20px\",\n                                        minHeight: \"350px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"8px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"20px\",\n                                                borderBottomLeftRadius: \"20px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-5\",\n                                            style: {\n                                                paddingLeft: \"3rem\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center\",\n                                                        style: {\n                                                            width: \"80px\",\n                                                            height: \"80px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"40\",\n                                                            height: \"40\",\n                                                            fill: \"#28a745\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 211,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-center fw-bold mb-4\",\n                                                    style: {\n                                                        fontSize: \"1.4rem\",\n                                                        color: \"#666\"\n                                                    },\n                                                    children: \"Benefits for companies\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-unstyled\",\n                                                    style: {\n                                                        fontSize: \"1rem\",\n                                                        lineHeight: \"1.8\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"More revenue, less risk\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \"—only pay for results. No fixed salaries or recruitment costs – just a clear commission per deal closed.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Access to skilled sales reps:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" Connect with motivated freelancers who are ready to grow your business.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Quick and easy collaboration:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 226,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" Post your brief, select the right sales agent, and let the deals flow.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row align-items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"fw-bold mb-4\",\n                                        style: {\n                                            fontSize: \"2.5rem\",\n                                            color: \"#333\"\n                                        },\n                                        children: \"Why we do what we do\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted mb-4\",\n                                        style: {\n                                            fontSize: \"1rem\",\n                                            lineHeight: \"1.6\",\n                                            fontStyle: \"italic\"\n                                        },\n                                        children: \"At DealClosed, we believe in solving real sales challenges with simple, effective solutions. Our platform has been developed in response to a clear need in the market:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"d-flex align-items-start mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"me-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-success\",\n                                                        style: {\n                                                            fontSize: \"1.2rem\"\n                                                        },\n                                                        children: \"✓\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"mb-2\",\n                                                            style: {\n                                                                fontSize: \"1.1rem\",\n                                                                fontWeight: \"600\"\n                                                            },\n                                                            children: \"For businesses:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            style: {\n                                                                fontSize: \"0.95rem\",\n                                                                lineHeight: \"1.6\",\n                                                                margin: \"0\"\n                                                            },\n                                                            children: \"Many companies struggle to find reliable sales talent without long-term contracts or high hiring costs. They need flexible, results-driven solutions to grow their revenue risk-free.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"d-flex align-items-start mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"me-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-success\",\n                                                        style: {\n                                                            fontSize: \"1.2rem\"\n                                                        },\n                                                        children: \"✓\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"mb-2\",\n                                                            style: {\n                                                                fontSize: \"1.1rem\",\n                                                                fontWeight: \"600\"\n                                                            },\n                                                            children: \"For sales agents:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            style: {\n                                                                fontSize: \"0.95rem\",\n                                                                lineHeight: \"1.6\",\n                                                                margin: \"0\"\n                                                            },\n                                                            children: \"Talented people often lack the freedom to work on their own terms or find opportunities that match their unique skill sets. Many are stuck in rigid working models that limit their potential. We give them the opportunity to work freely, wherever they are, and achieve success based on performance.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"btn btn-success rounded-pill px-4 py-2\",\n                                        style: {\n                                            fontSize: \"0.9rem\",\n                                            fontWeight: \"400\"\n                                        },\n                                        onClick: ()=>window.location.href = \"/about\",\n                                        children: \"Read more →\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"rounded shadow\",\n                                    style: {\n                                        height: \"400px\",\n                                        backgroundImage: \"url(/about.jpg)\",\n                                        backgroundSize: \"cover\",\n                                        backgroundPosition: \"center\",\n                                        backgroundRepeat: \"no-repeat\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 238,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row g-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm text-white\",\n                                    style: {\n                                        backgroundColor: \"#28a745\",\n                                        borderRadius: \"20px\",\n                                        minHeight: \"300px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card-body p-5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"fw-bold mb-4\",\n                                                style: {\n                                                    fontSize: \"1.8rem\"\n                                                },\n                                                children: \"Why should you choose us?\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mb-4\",\n                                                style: {\n                                                    fontSize: \"1rem\",\n                                                    lineHeight: \"1.6\"\n                                                },\n                                                children: \"To bridge this gap, we founded DealClosed. By linking companies with motivated freelance sales staff, we offer a win-win situation: companies achieve results without unnecessary overhead and sales employees get the flexibility to work how, when and where they want.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn px-4 py-2 rounded-pill\",\n                                                style: {\n                                                    backgroundColor: \"#1e7e34\",\n                                                    border: \"none\",\n                                                    color: \"white\",\n                                                    fontSize: \"0.9rem\",\n                                                    fontWeight: \"400\",\n                                                    transition: \"all 0.3s ease\"\n                                                },\n                                                onMouseEnter: (e)=>{\n                                                    const target = e.target;\n                                                    target.style.backgroundColor = \"white\";\n                                                    target.style.color = \"#28a745\";\n                                                },\n                                                onMouseLeave: (e)=>{\n                                                    const target = e.target;\n                                                    target.style.backgroundColor = \"#1e7e34\";\n                                                    target.style.color = \"white\";\n                                                },\n                                                onClick: ()=>window.location.href = \"/about\",\n                                                children: \"Learn more →\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"20px\",\n                                        minHeight: \"300px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"8px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"20px\",\n                                                borderBottomLeftRadius: \"20px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4 text-center d-flex flex-column justify-content-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center\",\n                                                        style: {\n                                                            width: \"60px\",\n                                                            height: \"60px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"30\",\n                                                            height: \"30\",\n                                                            fill: \"#28a745\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8 1a2.5 2.5 0 0 1 2.5 2.5V4h-5v-.5A2.5 2.5 0 0 1 8 1zm3.5 3v-.5a3.5 3.5 0 1 0-7 0V4H1v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4h-3.5zM2 5h12v9a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V5z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold mb-3\",\n                                                    style: {\n                                                        fontSize: \"1.2rem\"\n                                                    },\n                                                    children: \"Transparency\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted\",\n                                                    style: {\n                                                        fontSize: \"0.9rem\",\n                                                        lineHeight: \"1.5\"\n                                                    },\n                                                    children: \"With a simple 15% commission on successful deals, we guarantee fairness and clarity in every transaction.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"20px\",\n                                        minHeight: \"300px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"8px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"20px\",\n                                                borderBottomLeftRadius: \"20px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4 text-center d-flex flex-column justify-content-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center\",\n                                                        style: {\n                                                            width: \"60px\",\n                                                            height: \"60px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"30\",\n                                                            height: \"30\",\n                                                            fill: \"#28a745\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold mb-3\",\n                                                    style: {\n                                                        fontSize: \"1.2rem\"\n                                                    },\n                                                    children: \"Flexibility\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted\",\n                                                    style: {\n                                                        fontSize: \"0.9rem\",\n                                                        lineHeight: \"1.5\"\n                                                    },\n                                                    children: \"We embrace the future of work and offer sellers and companies freedom and choice.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"20px\",\n                                        minHeight: \"300px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"8px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"20px\",\n                                                borderBottomLeftRadius: \"20px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4 text-center d-flex flex-column justify-content-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center\",\n                                                        style: {\n                                                            width: \"60px\",\n                                                            height: \"60px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"30\",\n                                                            height: \"30\",\n                                                            fill: \"#28a745\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8.5 4.5a.5.5 0 0 0-1 0v3h-3a.5.5 0 0 0 0 1h3v3a.5.5 0 0 0 1 0v-3h3a.5.5 0 0 0 0-1h-3v-3z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold mb-3\",\n                                                    style: {\n                                                        fontSize: \"1.2rem\"\n                                                    },\n                                                    children: \"Focus on results\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted\",\n                                                    style: {\n                                                        fontSize: \"0.9rem\",\n                                                        lineHeight: \"1.5\"\n                                                    },\n                                                    children: \"We believe in rewarding results, not effort. We create a performance-oriented culture for everyone involved.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 305,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5 bg-light\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"fw-bold mb-4\",\n                            children: \"Assignments\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 441,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                            className: \"text-muted mb-4\",\n                            children: \"Latest Assignments\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 442,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card border-0 shadow-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-img-top bg-primary d-flex align-items-center justify-content-center\",\n                                            style: {\n                                                height: \"200px\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"60\",\n                                                        height: \"60\",\n                                                        fill: \"white\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854V1.146zM1.175 1a.146.146 0 0 0-.146.146v13.708c0 .08.066.146.146.146h13.65a.146.146 0 0 0 .146-.146V1.146a.146.146 0 0 0-.146-.146H1.175z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                        className: \"mt-2 mb-0\",\n                                                        children: \"Information Technology\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"badge bg-primary mb-2\",\n                                                    children: \"Information Technology\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-success fs-4 fw-bold\",\n                                                    children: \"$\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/assignments/5\",\n                                                        className: \"text-decoration-none text-dark\",\n                                                        children: \"Web application development\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted mb-2\",\n                                                    children: \"Test Assignment\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Payment Type:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        \" Hourly ($17.00)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mb-3\",\n                                                    children: \"Applications: 1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/login\",\n                                                    className: \"btn btn-success w-100\",\n                                                    children: \"Take This Deal\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 440,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 439,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white py-5 border-top\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-lg-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                            className: \"fw-bold text-success mb-4\",\n                                            children: \"DealClosed\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted mb-2\",\n                                            children: \"Amsterdam, The Netherlands\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Email:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 46\n                                                }, undefined),\n                                                \" <EMAIL>\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-lg-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                            className: \"fw-bold mb-3 text-success\",\n                                            children: \"Useful links\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"list-unstyled\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Home\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/about\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"About us\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/assignments\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Assignments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-lg-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                            className: \"fw-bold mb-3 text-success\",\n                                            children: \"Other links\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"list-unstyled\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/contact\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Contact Us\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/terms\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Terms of service\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/privacy\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Privacy policy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-lg-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                            className: \"fw-bold mb-3 text-success\",\n                                            children: \"Follow us\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"d-flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"btn btn-outline-secondary btn-sm rounded-circle\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"btn btn-outline-secondary btn-sm rounded-circle\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 513,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"btn btn-outline-secondary btn-sm rounded-circle\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"btn btn-outline-secondary btn-sm rounded-circle\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854V1.146zm4.943 12.248V6.169H2.542v7.225h2.401zm-1.2-8.212c.837 0 1.358-.554 1.358-1.248-.015-.709-.52-1.248-1.342-1.248-.822 0-1.359.54-1.359 1.248 0 .694.521 1.248 1.327 1.248h.016zm4.908 8.212V9.359c0-.216.016-.432.08-.586.173-.431.568-.878 1.232-.878.869 0 1.216.662 1.216 1.634v3.865h2.401V9.25c0-2.22-1.184-3.252-2.764-3.252-1.274 0-1.845.7-2.165 1.193v.025h-.016a5.54 5.54 0 0 1 .016-.025V6.169h-2.4c.03.678 0 7.225 0 7.225h2.4z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                            className: \"my-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 532,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row align-items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-md-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted mb-0\",\n                                        children: [\n                                            \"\\xa9 Copyright \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"DealClosed,\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 58\n                                            }, undefined),\n                                            \" All Rights Reserved\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-md-6 text-end\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/us.png\",\n                                            alt: \"English\",\n                                            width: \"20\",\n                                            className: \"me-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/nl.png\",\n                                            alt: \"Dutch\",\n                                            width: \"20\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 479,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 478,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_c = HomePage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomePage);\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n"));

/***/ })

});