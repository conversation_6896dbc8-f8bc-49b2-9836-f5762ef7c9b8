const cloudinary = require('cloudinary').v2;

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

/**
 * Upload image to Cloudinary
 * @param {string} filePath - Path to the file to upload
 * @param {string} folder - Cloudinary folder to upload to
 * @param {string} publicId - Custom public ID for the image
 * @returns {Promise<Object>} Upload result
 */
const uploadImage = async (filePath, folder = 'dealclosed', publicId = null) => {
  try {
    const options = {
      folder: folder,
      resource_type: 'image',
      quality: 'auto',
      fetch_format: 'auto',
    };

    if (publicId) {
      options.public_id = publicId;
    }

    const result = await cloudinary.uploader.upload(filePath, options);
    return {
      success: true,
      url: result.secure_url,
      publicId: result.public_id,
      data: result
    };
  } catch (error) {
    console.error('Cloudinary upload error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Upload profile image
 * @param {string} filePath - Path to the profile image
 * @param {string} userId - User ID for naming
 * @returns {Promise<Object>} Upload result
 */
const uploadProfileImage = async (filePath, userId) => {
  return uploadImage(filePath, 'dealclosed/profiles', `profile_${userId}`);
};

/**
 * Delete image from Cloudinary
 * @param {string} publicId - Public ID of the image to delete
 * @returns {Promise<Object>} Delete result
 */
const deleteImage = async (publicId) => {
  try {
    const result = await cloudinary.uploader.destroy(publicId);
    return {
      success: true,
      result: result
    };
  } catch (error) {
    console.error('Cloudinary delete error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Generate transformation URL
 * @param {string} publicId - Public ID of the image
 * @param {Object} transformations - Transformation options
 * @returns {string} Transformed image URL
 */
const getTransformedUrl = (publicId, transformations = {}) => {
  return cloudinary.url(publicId, {
    quality: 'auto',
    fetch_format: 'auto',
    ...transformations
  });
};

/**
 * Get optimized profile image URL
 * @param {string} publicId - Public ID of the profile image
 * @param {number} size - Size of the image (default: 200)
 * @returns {string} Optimized image URL
 */
const getProfileImageUrl = (publicId, size = 200) => {
  return getTransformedUrl(publicId, {
    width: size,
    height: size,
    crop: 'fill',
    gravity: 'face'
  });
};

module.exports = {
  cloudinary,
  uploadImage,
  uploadProfileImage,
  deleteImage,
  getTransformedUrl,
  getProfileImageUrl
};
