require('dotenv').config();

console.log('📧 Email Configuration Check:');
console.log('================================');
console.log(`SMTP_HOST: ${process.env.SMTP_HOST}`);
console.log(`SMTP_PORT: ${process.env.SMTP_PORT}`);
console.log(`SMTP_SECURE: ${process.env.SMTP_SECURE}`);
console.log(`SMTP_USERNAME: ${process.env.SMTP_USERNAME}`);
console.log(`SMTP_PASSWORD: ${process.env.SMTP_PASSWORD ? '***HIDDEN***' : 'NOT SET'}`);
console.log(`SMTP_FROM_EMAIL: ${process.env.SMTP_FROM_EMAIL}`);
console.log(`SMTP_FROM_NAME: ${process.env.SMTP_FROM_NAME}`);
console.log(`FRONTEND_URL: ${process.env.FRONTEND_URL}`);
console.log('================================');

// Check if all required variables are set
const required = ['SMTP_HOST', 'SMTP_PORT', 'SMTP_USERNAME', 'SMTP_PASSWORD', 'SMTP_FROM_EMAIL'];
const missing = required.filter(key => !process.env[key]);

if (missing.length > 0) {
  console.log('❌ Missing required environment variables:');
  missing.forEach(key => console.log(`   - ${key}`));
} else {
  console.log('✅ All required email environment variables are set');
}
