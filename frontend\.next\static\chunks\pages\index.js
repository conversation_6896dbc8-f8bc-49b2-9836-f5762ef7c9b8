/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/index"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CFAST%5CFreelance%5CProject9_SalesPlatform%5CDealClosed%5Cfrontend%5Csrc%5Cpages%5Cindex.tsx&page=%2F!":
/*!****************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CFAST%5CFreelance%5CProject9_SalesPlatform%5CDealClosed%5Cfrontend%5Csrc%5Cpages%5Cindex.tsx&page=%2F! ***!
  \****************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/index.tsx */ \"./src/pages/index.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1EJTNBJTVDRkFTVCU1Q0ZyZWVsYW5jZSU1Q1Byb2plY3Q5X1NhbGVzUGxhdGZvcm0lNUNEZWFsQ2xvc2VkJTVDZnJvbnRlbmQlNUNzcmMlNUNwYWdlcyU1Q2luZGV4LnRzeCZwYWdlPSUyRiEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyxvREFBdUI7QUFDOUM7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzBiM2YiXSwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgKHdpbmRvdy5fX05FWFRfUCA9IHdpbmRvdy5fX05FWFRfUCB8fCBbXSkucHVzaChbXG4gICAgICBcIi9cIixcbiAgICAgIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHJlcXVpcmUoXCIuL3NyYy9wYWdlcy9pbmRleC50c3hcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL1wiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CFAST%5CFreelance%5CProject9_SalesPlatform%5CDealClosed%5Cfrontend%5Csrc%5Cpages%5Cindex.tsx&page=%2F!\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js ***!
  \*****************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    PrefetchKind: function() {\n        return PrefetchKind;\n    },\n    ACTION_REFRESH: function() {\n        return ACTION_REFRESH;\n    },\n    ACTION_NAVIGATE: function() {\n        return ACTION_NAVIGATE;\n    },\n    ACTION_RESTORE: function() {\n        return ACTION_RESTORE;\n    },\n    ACTION_SERVER_PATCH: function() {\n        return ACTION_SERVER_PATCH;\n    },\n    ACTION_PREFETCH: function() {\n        return ACTION_PREFETCH;\n    },\n    ACTION_FAST_REFRESH: function() {\n        return ACTION_FAST_REFRESH;\n    },\n    ACTION_SERVER_ACTION: function() {\n        return ACTION_SERVER_ACTION;\n    },\n    isThenable: function() {\n        return isThenable;\n    }\n});\nconst ACTION_REFRESH = \"refresh\";\nconst ACTION_NAVIGATE = \"navigate\";\nconst ACTION_RESTORE = \"restore\";\nconst ACTION_SERVER_PATCH = \"server-patch\";\nconst ACTION_PREFETCH = \"prefetch\";\nconst ACTION_FAST_REFRESH = \"fast-refresh\";\nconst ACTION_SERVER_ACTION = \"server-action\";\nvar PrefetchKind;\n(function(PrefetchKind) {\n    PrefetchKind[\"AUTO\"] = \"auto\";\n    PrefetchKind[\"FULL\"] = \"full\";\n    PrefetchKind[\"TEMPORARY\"] = \"temporary\";\n})(PrefetchKind || (PrefetchKind = {}));\nfunction isThenable(value) {\n    // TODO: We don't gain anything from this abstraction. It's unsound, and only\n    // makes sense in the specific places where we use it. So it's better to keep\n    // the type coercion inline, instead of leaking this to other places in\n    // the codebase.\n    return value && (typeof value === \"object\" || typeof value === \"function\") && typeof value.then === \"function\";\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=router-reducer-types.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/get-domain-locale.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/client/get-domain-locale.js ***!
  \************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getDomainLocale\", ({\n    enumerable: true,\n    get: function() {\n        return getDomainLocale;\n    }\n}));\nconst _normalizetrailingslash = __webpack_require__(/*! ./normalize-trailing-slash */ \"./node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst basePath =  false || \"\";\nfunction getDomainLocale(path, locale, locales, domainLocales) {\n    if (false) {} else {\n        return false;\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-domain-locale.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/get-domain-locale.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/link.js":
/*!***********************************************!*\
  !*** ./node_modules/next/dist/client/link.js ***!
  \***********************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nconst _resolvehref = __webpack_require__(/*! ./resolve-href */ \"./node_modules/next/dist/client/resolve-href.js\");\nconst _islocalurl = __webpack_require__(/*! ../shared/lib/router/utils/is-local-url */ \"./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _formaturl = __webpack_require__(/*! ../shared/lib/router/utils/format-url */ \"./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _utils = __webpack_require__(/*! ../shared/lib/utils */ \"./node_modules/next/dist/shared/lib/utils.js\");\nconst _addlocale = __webpack_require__(/*! ./add-locale */ \"./node_modules/next/dist/client/add-locale.js\");\nconst _routercontextsharedruntime = __webpack_require__(/*! ../shared/lib/router-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/router-context.shared-runtime.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../shared/lib/app-router-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _useintersection = __webpack_require__(/*! ./use-intersection */ \"./node_modules/next/dist/client/use-intersection.js\");\nconst _getdomainlocale = __webpack_require__(/*! ./get-domain-locale */ \"./node_modules/next/dist/client/get-domain-locale.js\");\nconst _addbasepath = __webpack_require__(/*! ./add-base-path */ \"./node_modules/next/dist/client/add-base-path.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./components/router-reducer/router-reducer-types */ \"./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst prefetched = new Set();\nfunction prefetch(router, href, as, options, appOptions, isAppRouter) {\n    if (false) {}\n    // app-router supports external urls out of the box so it shouldn't short-circuit here as support for e.g. `replace` is added in the app-router.\n    if (!isAppRouter && !(0, _islocalurl.isLocalURL)(href)) {\n        return;\n    }\n    // We should only dedupe requests when experimental.optimisticClientCache is\n    // disabled.\n    if (!options.bypassPrefetchedCheck) {\n        const locale = typeof options.locale !== \"undefined\" ? options.locale : \"locale\" in router ? router.locale : undefined;\n        const prefetchedKey = href + \"%\" + as + \"%\" + locale;\n        // If we've already fetched the key, then don't prefetch it again!\n        if (prefetched.has(prefetchedKey)) {\n            return;\n        }\n        // Mark this URL as prefetched.\n        prefetched.add(prefetchedKey);\n    }\n    const prefetchPromise = isAppRouter ? router.prefetch(href, appOptions) : router.prefetch(href, as, options);\n    // Prefetch the JSON page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    Promise.resolve(prefetchPromise).catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute(\"target\");\n    return target && target !== \"_self\" || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === \"A\";\n    if (isAnchorNodeName && (isModifiedEvent(e) || // app-router supports external urls out of the box so it shouldn't short-circuit here as support for e.g. `replace` is added in the app-router.\n    !isAppRouter && !(0, _islocalurl.isLocalURL)(href))) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        // If the router is an NextRouter instance it will have `beforePopState`\n        const routerScroll = scroll != null ? scroll : true;\n        if (\"beforePopState\" in router) {\n            router[replace ? \"replace\" : \"push\"](href, as, {\n                shallow,\n                locale,\n                scroll: routerScroll\n            });\n        } else {\n            router[replace ? \"replace\" : \"push\"](as || href, {\n                scroll: routerScroll\n            });\n        }\n    };\n    if (isAppRouter) {\n        _react.default.startTransition(navigate);\n    } else {\n        navigate();\n    }\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === \"string\") {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\n/**\n * React Component that enables client-side transitions between routes.\n */ const Link = /*#__PURE__*/ _s(_react.default.forwardRef(_c = _s(function LinkComponent(props, forwardedRef) {\n    _s();\n    let children;\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, locale, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === \"string\" || typeof children === \"number\")) {\n        children = /*#__PURE__*/ _react.default.createElement(\"a\", null, children);\n    }\n    const pagesRouter = _react.default.useContext(_routercontextsharedruntime.RouterContext);\n    const appRouter = _react.default.useContext(_approutercontextsharedruntime.AppRouterContext);\n    const router = pagesRouter != null ? pagesRouter : appRouter;\n    // We're in the app directory if there is no pages router.\n    const isAppRouter = !pagesRouter;\n    const prefetchEnabled = prefetchProp !== false;\n    /**\n     * The possible states for prefetch are:\n     * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n     * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n     * - false: we will not prefetch if in the viewport at all\n     */ const appPrefetchKind = prefetchProp === null ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;\n    if (true) {\n        function createPropError(args) {\n            return new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0));\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === \"href\") {\n                if (props[key] == null || typeof props[key] !== \"string\" && typeof props[key] !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: props[key] === null ? \"null\" : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            locale: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === \"as\") {\n                if (props[key] && valType !== \"string\" && valType !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"locale\") {\n                if (props[key] && valType !== \"string\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"onClick\" || key === \"onMouseEnter\" || key === \"onTouchStart\") {\n                if (props[key] && valType !== \"function\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`function`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"replace\" || key === \"scroll\" || key === \"shallow\" || key === \"passHref\" || key === \"prefetch\" || key === \"legacyBehavior\") {\n                if (props[key] != null && valType !== \"boolean\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`boolean`\",\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const hasWarned = _react.default.useRef(false);\n        if (props.prefetch && !hasWarned.current && !isAppRouter) {\n            hasWarned.current = true;\n            console.warn(\"Next.js auto-prefetches automatically based on viewport. The prefetch attribute is no longer needed. More: https://nextjs.org/docs/messages/prefetch-true-deprecated\");\n        }\n    }\n    if (true) {\n        if (isAppRouter && !asProp) {\n            let href;\n            if (typeof hrefProp === \"string\") {\n                href = hrefProp;\n            } else if (typeof hrefProp === \"object\" && typeof hrefProp.pathname === \"string\") {\n                href = hrefProp.pathname;\n            }\n            if (href) {\n                const hasDynamicSegment = href.split(\"/\").some((segment)=>segment.startsWith(\"[\") && segment.endsWith(\"]\"));\n                if (hasDynamicSegment) {\n                    throw new Error(\"Dynamic href `\" + href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\");\n                }\n            }\n        }\n    }\n    const { href, as } = _react.default.useMemo(()=>{\n        if (!pagesRouter) {\n            const resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n        const [resolvedHref, resolvedAs] = (0, _resolvehref.resolveHref)(pagesRouter, hrefProp, true);\n        return {\n            href: resolvedHref,\n            as: asProp ? (0, _resolvehref.resolveHref)(pagesRouter, asProp) : resolvedAs || resolvedHref\n        };\n    }, [\n        pagesRouter,\n        hrefProp,\n        asProp\n    ]);\n    const previousHref = _react.default.useRef(href);\n    const previousAs = _react.default.useRef(as);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\");\n                }\n                throw new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0));\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === \"a\") {\n                throw new Error(\"Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor\");\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === \"object\" && child.ref : forwardedRef;\n    const [setIntersectionRef, isVisible, resetVisible] = (0, _useintersection.useIntersection)({\n        rootMargin: \"200px\"\n    });\n    const setRef = _react.default.useCallback((el)=>{\n        // Before the link getting observed, check if visible state need to be reset\n        if (previousAs.current !== as || previousHref.current !== href) {\n            resetVisible();\n            previousAs.current = as;\n            previousHref.current = href;\n        }\n        setIntersectionRef(el);\n        if (childRef) {\n            if (typeof childRef === \"function\") childRef(el);\n            else if (typeof childRef === \"object\") {\n                childRef.current = el;\n            }\n        }\n    }, [\n        as,\n        childRef,\n        href,\n        resetVisible,\n        setIntersectionRef\n    ]);\n    // Prefetch the URL if we haven't already and it's visible.\n    _react.default.useEffect(()=>{\n        // in dev, we only prefetch on hover to avoid wasting resources as the prefetch will trigger compiling the page.\n        if (true) {\n            return;\n        }\n        if (!router) {\n            return;\n        }\n        // If we don't need to prefetch the URL, don't do prefetch.\n        if (!isVisible || !prefetchEnabled) {\n            return;\n        }\n        // Prefetch the URL.\n        prefetch(router, href, as, {\n            locale\n        }, {\n            kind: appPrefetchKind\n        }, isAppRouter);\n    }, [\n        as,\n        href,\n        isVisible,\n        locale,\n        prefetchEnabled,\n        pagesRouter == null ? void 0 : pagesRouter.locale,\n        router,\n        isAppRouter,\n        appPrefetchKind\n    ]);\n    const childProps = {\n        ref: setRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.');\n                }\n            }\n            if (!legacyBehavior && typeof onClick === \"function\") {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === \"function\") {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === \"function\") {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === \"function\") {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if ((!prefetchEnabled || \"development\" === \"development\") && isAppRouter) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            }, {\n                kind: appPrefetchKind\n            }, isAppRouter);\n        },\n        onTouchStart (e) {\n            if (!legacyBehavior && typeof onTouchStartProp === \"function\") {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === \"function\") {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled && isAppRouter) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            }, {\n                kind: appPrefetchKind\n            }, isAppRouter);\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the domain and locale.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === \"a\" && !(\"href\" in child.props)) {\n        const curLocale = typeof locale !== \"undefined\" ? locale : pagesRouter == null ? void 0 : pagesRouter.locale;\n        // we only render domain locales if we are currently on a domain locale\n        // so that locale links are still visitable in development/preview envs\n        const localeDomain = (pagesRouter == null ? void 0 : pagesRouter.isLocaleDomain) && (0, _getdomainlocale.getDomainLocale)(as, curLocale, pagesRouter == null ? void 0 : pagesRouter.locales, pagesRouter == null ? void 0 : pagesRouter.domainLocales);\n        childProps.href = localeDomain || (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, curLocale, pagesRouter == null ? void 0 : pagesRouter.defaultLocale));\n    }\n    return legacyBehavior ? /*#__PURE__*/ _react.default.cloneElement(child, childProps) : /*#__PURE__*/ _react.default.createElement(\"a\", {\n        ...restProps,\n        ...childProps\n    }, children);\n}, \"wKD5mb5mk47bkaStGb/Fvd6RWZE=\")), \"wKD5mb5mk47bkaStGb/Fvd6RWZE=\");\n_c1 = Link;\nconst _default = Link;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Link$_react.default.forwardRef\");\n$RefreshReg$(_c1, \"Link\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/link.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/use-intersection.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/use-intersection.js ***!
  \***********************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useIntersection\", ({\n    enumerable: true,\n    get: function() {\n        return useIntersection;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\nconst _requestidlecallback = __webpack_require__(/*! ./request-idle-callback */ \"./node_modules/next/dist/client/request-idle-callback.js\");\nconst hasIntersectionObserver = typeof IntersectionObserver === \"function\";\nconst observers = new Map();\nconst idList = [];\nfunction createObserver(options) {\n    const id = {\n        root: options.root || null,\n        margin: options.rootMargin || \"\"\n    };\n    const existing = idList.find((obj)=>obj.root === id.root && obj.margin === id.margin);\n    let instance;\n    if (existing) {\n        instance = observers.get(existing);\n        if (instance) {\n            return instance;\n        }\n    }\n    const elements = new Map();\n    const observer = new IntersectionObserver((entries)=>{\n        entries.forEach((entry)=>{\n            const callback = elements.get(entry.target);\n            const isVisible = entry.isIntersecting || entry.intersectionRatio > 0;\n            if (callback && isVisible) {\n                callback(isVisible);\n            }\n        });\n    }, options);\n    instance = {\n        id,\n        observer,\n        elements\n    };\n    idList.push(id);\n    observers.set(id, instance);\n    return instance;\n}\nfunction observe(element, callback, options) {\n    const { id, observer, elements } = createObserver(options);\n    elements.set(element, callback);\n    observer.observe(element);\n    return function unobserve() {\n        elements.delete(element);\n        observer.unobserve(element);\n        // Destroy observer when there's nothing left to watch:\n        if (elements.size === 0) {\n            observer.disconnect();\n            observers.delete(id);\n            const index = idList.findIndex((obj)=>obj.root === id.root && obj.margin === id.margin);\n            if (index > -1) {\n                idList.splice(index, 1);\n            }\n        }\n    };\n}\nfunction useIntersection(param) {\n    let { rootRef, rootMargin, disabled } = param;\n    const isDisabled = disabled || !hasIntersectionObserver;\n    const [visible, setVisible] = (0, _react.useState)(false);\n    const elementRef = (0, _react.useRef)(null);\n    const setElement = (0, _react.useCallback)((element)=>{\n        elementRef.current = element;\n    }, []);\n    (0, _react.useEffect)(()=>{\n        if (hasIntersectionObserver) {\n            if (isDisabled || visible) return;\n            const element = elementRef.current;\n            if (element && element.tagName) {\n                const unobserve = observe(element, (isVisible)=>isVisible && setVisible(isVisible), {\n                    root: rootRef == null ? void 0 : rootRef.current,\n                    rootMargin\n                });\n                return unobserve;\n            }\n        } else {\n            if (!visible) {\n                const idleCallback = (0, _requestidlecallback.requestIdleCallback)(()=>setVisible(true));\n                return ()=>(0, _requestidlecallback.cancelIdleCallback)(idleCallback);\n            }\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        isDisabled,\n        rootMargin,\n        rootRef,\n        visible,\n        elementRef.current\n    ]);\n    const resetVisible = (0, _react.useCallback)(()=>{\n        setVisible(false);\n    }, []);\n    return [\n        setElement,\n        visible,\n        resetVisible\n    ];\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-intersection.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/use-intersection.js\n"));

/***/ }),

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst HomePage = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"DealClosed Partner - The platform where sales come together\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"At DealClosed, we connect businesses with freelance sales agents in a win-win situation. Businesses gain access to top sales agents, while agents have the freedom to work flexibly—anytime, anywhere.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"navbar navbar-expand-lg navbar-light bg-white shadow-sm py-3 fixed-top\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"navbar-brand d-flex align-items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/assets/img/default.png\",\n                                    alt: \"DealClosed Logo\",\n                                    width: \"40\",\n                                    height: \"40\",\n                                    className: \"me-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"fw-bold text-success\",\n                                    style: {\n                                        fontSize: \"1.7rem\"\n                                    },\n                                    children: \"DealClosed\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"navbar-toggler\",\n                            type: \"button\",\n                            \"data-bs-toggle\": \"collapse\",\n                            \"data-bs-target\": \"#navbarNav\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"navbar-toggler-icon\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"collapse navbar-collapse\",\n                            id: \"navbarNav\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"navbar-nav me-auto ms-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\",\n                                                className: \"nav-link text-success\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"At home\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 35,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/assignments\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Commands\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 38,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/freelancers\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Discover freelancers\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 41,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/companies\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Find company\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/courses\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Courses\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/ranking\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Rankings\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/blog\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Blogs\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"d-flex gap-2 align-items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/login\",\n                                            className: \"nav-link text-dark\",\n                                            style: {\n                                                fontWeight: \"500\",\n                                                fontSize: \"1.05rem\"\n                                            },\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/register\",\n                                            className: \"btn btn-success rounded-pill px-4 py-2\",\n                                            style: {\n                                                fontWeight: \"500\",\n                                                fontSize: \"15px\"\n                                            },\n                                            children: \"Get started\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"position-relative\",\n                style: {\n                    backgroundImage: \"url(/assets/img/default.png)\",\n                    backgroundSize: \"cover\",\n                    backgroundPosition: \"center\",\n                    minHeight: \"100vh\",\n                    marginTop: \"80px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container h-100 d-flex align-items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row w-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-lg-8 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"display-3 fw-bold mb-4\",\n                                    style: {\n                                        fontSize: \"3.5rem\",\n                                        lineHeight: \"1.2\"\n                                    },\n                                    children: [\n                                        \"Welcome to Deal Closed,\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 40\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"2.8rem\"\n                                            },\n                                            children: \"the platform where sales come together\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"lead mb-4\",\n                                    style: {\n                                        fontSize: \"1.25rem\",\n                                        opacity: \"0.95\",\n                                        maxWidth: \"600px\"\n                                    },\n                                    children: \"At DealClosed, we connect companies with freelance sales agents in a win-win situation. Businesses get access to top sales agents, while agents have the freedom to work flexibly — anytime, anywhere.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn btn-lg px-5 py-3 rounded-pill\",\n                                    style: {\n                                        backgroundColor: \"transparent\",\n                                        border: \"2px solid white\",\n                                        color: \"white\",\n                                        fontSize: \"16px\",\n                                        fontWeight: \"400\",\n                                        transition: \"all 0.3s ease\"\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        const target = e.target;\n                                        target.style.backgroundColor = \"#28a745\";\n                                        target.style.borderColor = \"#28a745\";\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        const target = e.target;\n                                        target.style.backgroundColor = \"transparent\";\n                                        target.style.borderColor = \"white\";\n                                    },\n                                    onClick: ()=>window.location.href = \"/register\",\n                                    children: \"Get started\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-3\",\n                style: {\n                    backgroundColor: \"#f8f9fa\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                        className: \"mb-0 text-muted\",\n                        style: {\n                            fontSize: \"0.9rem\"\n                        },\n                        children: \"Connecting companies with freelance sales associates\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5\",\n                style: {\n                    backgroundColor: \"#f8f9fa\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-lg-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"fw-bold mb-4\",\n                                    style: {\n                                        fontSize: \"2.5rem\",\n                                        color: \"#333\"\n                                    },\n                                    children: \"Connecting companies with freelance sales agents who deliver.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-3\",\n                                    style: {\n                                        fontSize: \"1.1rem\",\n                                        lineHeight: \"1.6\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"DealClosed\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" is the platform where companies and freelance sales agents meet. No expensive contracts or recruitment agencies – just collaboration based on performance.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-3\",\n                                    style: {\n                                        fontSize: \"1.1rem\",\n                                        lineHeight: \"1.6\"\n                                    },\n                                    children: \"Whether you're looking to close deals or find someone who can do it for you, DealClosed is the place to be.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        fontSize: \"1.1rem\",\n                                        lineHeight: \"1.6\",\n                                        color: \"#666\",\n                                        fontStyle: \"italic\"\n                                    },\n                                    children: \"Work flexibly. Earn fairly. Grow together.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5 bg-light\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row g-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"20px\",\n                                        minHeight: \"350px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"8px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"20px\",\n                                                borderBottomLeftRadius: \"20px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-5\",\n                                            style: {\n                                                paddingLeft: \"3rem\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center\",\n                                                        style: {\n                                                            width: \"80px\",\n                                                            height: \"80px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"40\",\n                                                            height: \"40\",\n                                                            fill: \"#28a745\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M7 14s-1 0-1-1 1-4 5-4 5 3 5 4-1 1-1 1H7zm4-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 166,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M5.216 14A2.238 2.238 0 0 1 5 13c0-1.355.68-2.75 1.936-3.72A6.325 6.325 0 0 0 5 9c-4 0-5 3-5 4s1 1 1 1h4.216z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 167,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M4.5 8a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 168,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-center fw-bold mb-4\",\n                                                    style: {\n                                                        fontSize: \"1.4rem\",\n                                                        color: \"#666\"\n                                                    },\n                                                    children: \"Benefits for freelance sales associates\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-unstyled\",\n                                                    style: {\n                                                        fontSize: \"1rem\",\n                                                        lineHeight: \"1.8\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Work wherever you want\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 175,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \"—at home, in a caf\\xe9, or on the go—you choose when and where you work.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Earn attractive commissions:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 179,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" Get a fair fee per deal closed. Your success is in your hands.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Diverse opportunities:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 183,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" discover a wide range of projects and companies that match your skills and interests\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"20px\",\n                                        minHeight: \"350px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"8px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"20px\",\n                                                borderBottomLeftRadius: \"20px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-5\",\n                                            style: {\n                                                paddingLeft: \"3rem\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center\",\n                                                        style: {\n                                                            width: \"80px\",\n                                                            height: \"80px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"40\",\n                                                            height: \"40\",\n                                                            fill: \"#28a745\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 206,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-center fw-bold mb-4\",\n                                                    style: {\n                                                        fontSize: \"1.4rem\",\n                                                        color: \"#666\"\n                                                    },\n                                                    children: \"Benefits for companies\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-unstyled\",\n                                                    style: {\n                                                        fontSize: \"1rem\",\n                                                        lineHeight: \"1.8\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"More revenue, less risk\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 213,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \"—only pay for results. No fixed salaries or recruitment costs – just a clear commission per deal closed.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Access to skilled sales reps:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" Connect with motivated freelancers who are ready to grow your business.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Quick and easy collaboration:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" Post your brief, select the right sales agent, and let the deals flow.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row align-items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"fw-bold mb-4\",\n                                        style: {\n                                            fontSize: \"2.5rem\",\n                                            color: \"#333\"\n                                        },\n                                        children: \"Why we do what we do\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted mb-4\",\n                                        style: {\n                                            fontSize: \"1rem\",\n                                            lineHeight: \"1.6\",\n                                            fontStyle: \"italic\"\n                                        },\n                                        children: \"At DealClosed, we believe in solving real sales challenges with simple, effective solutions. Our platform has been developed in response to a clear need in the market:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"d-flex align-items-start mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"me-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-success rounded-circle d-flex align-items-center justify-content-center\",\n                                                        style: {\n                                                            width: \"24px\",\n                                                            height: \"24px\",\n                                                            minWidth: \"24px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"14\",\n                                                            height: \"14\",\n                                                            fill: \"white\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"mb-2\",\n                                                            style: {\n                                                                fontSize: \"1.1rem\",\n                                                                fontWeight: \"600\"\n                                                            },\n                                                            children: \"For businesses:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            style: {\n                                                                fontSize: \"0.95rem\",\n                                                                lineHeight: \"1.6\",\n                                                                margin: \"0\"\n                                                            },\n                                                            children: \"Many companies struggle to find reliable sales talent without long-term contracts or high hiring costs. They need flexible, results-driven solutions to grow their revenue risk-free.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"d-flex align-items-start mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"me-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-success rounded-circle d-flex align-items-center justify-content-center\",\n                                                        style: {\n                                                            width: \"24px\",\n                                                            height: \"24px\",\n                                                            minWidth: \"24px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"14\",\n                                                            height: \"14\",\n                                                            fill: \"white\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"mb-2\",\n                                                            style: {\n                                                                fontSize: \"1.1rem\",\n                                                                fontWeight: \"600\"\n                                                            },\n                                                            children: \"For sales agents:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            style: {\n                                                                fontSize: \"0.95rem\",\n                                                                lineHeight: \"1.6\",\n                                                                margin: \"0\"\n                                                            },\n                                                            children: \"Talented people often lack the freedom to work on their own terms or find opportunities that match their unique skill sets. Many are stuck in rigid working models that limit their potential. We give them the opportunity to work freely, wherever they are, and achieve success based on performance.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"btn btn-success rounded-pill px-4 py-2\",\n                                        style: {\n                                            fontSize: \"0.9rem\",\n                                            fontWeight: \"400\"\n                                        },\n                                        onClick: ()=>window.location.href = \"/about\",\n                                        children: \"Read more →\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/assets/img/default.png\",\n                                    alt: \"Why we do what we do\",\n                                    className: \"img-fluid rounded shadow\",\n                                    style: {\n                                        width: \"100%\",\n                                        height: \"400px\",\n                                        objectFit: \"cover\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row g-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm text-white\",\n                                    style: {\n                                        backgroundColor: \"#28a745\",\n                                        borderRadius: \"20px\",\n                                        minHeight: \"400px\",\n                                        aspectRatio: \"1\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card-body p-4 d-flex flex-column justify-content-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"fw-bold mb-4\",\n                                                style: {\n                                                    fontSize: \"1.6rem\"\n                                                },\n                                                children: \"Why should you choose us?\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mb-4\",\n                                                style: {\n                                                    fontSize: \"0.9rem\",\n                                                    lineHeight: \"1.6\"\n                                                },\n                                                children: \"To bridge this gap, we founded DealClosed. By linking companies with motivated freelance sales staff, we offer a win-win situation: companies achieve results without unnecessary overhead and sales employees get the flexibility to work how, when and where they want.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn px-4 py-2 rounded-pill\",\n                                                style: {\n                                                    backgroundColor: \"#1e7e34\",\n                                                    border: \"none\",\n                                                    color: \"white\",\n                                                    fontSize: \"0.9rem\",\n                                                    fontWeight: \"400\",\n                                                    transition: \"all 0.3s ease\"\n                                                },\n                                                onMouseEnter: (e)=>{\n                                                    const target = e.target;\n                                                    target.style.backgroundColor = \"white\";\n                                                    target.style.color = \"#28a745\";\n                                                },\n                                                onMouseLeave: (e)=>{\n                                                    const target = e.target;\n                                                    target.style.backgroundColor = \"#1e7e34\";\n                                                    target.style.color = \"white\";\n                                                },\n                                                onClick: ()=>window.location.href = \"/about\",\n                                                children: \"Learn more →\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"20px\",\n                                        minHeight: \"400px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"8px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"20px\",\n                                                borderBottomLeftRadius: \"20px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4 text-center d-flex flex-column justify-content-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center\",\n                                                        style: {\n                                                            width: \"60px\",\n                                                            height: \"60px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"30\",\n                                                            height: \"30\",\n                                                            fill: \"#28a745\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8 1a2.5 2.5 0 0 1 2.5 2.5V4h-5v-.5A2.5 2.5 0 0 1 8 1zm3.5 3v-.5a3.5 3.5 0 1 0-7 0V4H1v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4h-3.5zM2 5h12v9a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V5z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold mb-3\",\n                                                    style: {\n                                                        fontSize: \"1.2rem\"\n                                                    },\n                                                    children: \"Transparency\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted\",\n                                                    style: {\n                                                        fontSize: \"0.9rem\",\n                                                        lineHeight: \"1.5\"\n                                                    },\n                                                    children: \"With a simple 15% commission on successful deals, we guarantee fairness and clarity in every transaction.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"20px\",\n                                        minHeight: \"400px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"8px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"20px\",\n                                                borderBottomLeftRadius: \"20px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4 text-center d-flex flex-column justify-content-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center\",\n                                                        style: {\n                                                            width: \"60px\",\n                                                            height: \"60px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"30\",\n                                                            height: \"30\",\n                                                            fill: \"#28a745\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 390,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold mb-3\",\n                                                    style: {\n                                                        fontSize: \"1.2rem\"\n                                                    },\n                                                    children: \"Flexibility\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted\",\n                                                    style: {\n                                                        fontSize: \"0.9rem\",\n                                                        lineHeight: \"1.5\"\n                                                    },\n                                                    children: \"We embrace the future of work and offer sellers and companies freedom and choice.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"20px\",\n                                        minHeight: \"400px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"8px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"20px\",\n                                                borderBottomLeftRadius: \"20px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4 text-center d-flex flex-column justify-content-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center\",\n                                                        style: {\n                                                            width: \"60px\",\n                                                            height: \"60px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"30\",\n                                                            height: \"30\",\n                                                            fill: \"#28a745\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8.5 4.5a.5.5 0 0 0-1 0v3h-3a.5.5 0 0 0 0 1h3v3a.5.5 0 0 0 1 0v-3h3a.5.5 0 0 0 0-1h-3v-3z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold mb-3\",\n                                                    style: {\n                                                        fontSize: \"1.2rem\"\n                                                    },\n                                                    children: \"Focus on results\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted\",\n                                                    style: {\n                                                        fontSize: \"0.9rem\",\n                                                        lineHeight: \"1.5\"\n                                                    },\n                                                    children: \"We believe in rewarding results, not effort. We create a performance-oriented culture for everyone involved.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 304,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5 bg-light\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"fw-bold mb-4\",\n                            children: \"Assignments\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                            className: \"text-muted mb-4\",\n                            children: \"Latest Assignments\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 441,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card border-0 shadow-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-img-top bg-primary d-flex align-items-center justify-content-center\",\n                                            style: {\n                                                height: \"200px\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"60\",\n                                                        height: \"60\",\n                                                        fill: \"white\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854V1.146zM1.175 1a.146.146 0 0 0-.146.146v13.708c0 .08.066.146.146.146h13.65a.146.146 0 0 0 .146-.146V1.146a.146.146 0 0 0-.146-.146H1.175z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                        className: \"mt-2 mb-0\",\n                                                        children: \"Information Technology\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"badge bg-primary mb-2\",\n                                                    children: \"Information Technology\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-success fs-4 fw-bold\",\n                                                    children: \"$\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/assignments/5\",\n                                                        className: \"text-decoration-none text-dark\",\n                                                        children: \"Web application development\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted mb-2\",\n                                                    children: \"Test Assignment\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Payment Type:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        \" Hourly ($17.00)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mb-3\",\n                                                    children: \"Applications: 1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/login\",\n                                                    className: \"btn btn-success w-100\",\n                                                    children: \"Take This Deal\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 439,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 438,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white py-5 border-top\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-lg-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                            className: \"fw-bold text-success mb-4\",\n                                            children: \"DealClosed\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted mb-2\",\n                                            children: \"Amsterdam, The Netherlands\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 482,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Email:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 46\n                                                }, undefined),\n                                                \" <EMAIL>\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-lg-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                            className: \"fw-bold mb-3 text-success\",\n                                            children: \"Useful links\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"list-unstyled\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Home\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/about\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"About us\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/assignments\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Assignments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-lg-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                            className: \"fw-bold mb-3 text-success\",\n                                            children: \"Other links\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"list-unstyled\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/contact\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Contact Us\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/terms\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Terms of service\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/privacy\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Privacy policy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-lg-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                            className: \"fw-bold mb-3 text-success\",\n                                            children: \"Follow us\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"d-flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"btn btn-outline-secondary btn-sm rounded-circle\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"btn btn-outline-secondary btn-sm rounded-circle\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"btn btn-outline-secondary btn-sm rounded-circle\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"btn btn-outline-secondary btn-sm rounded-circle\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854V1.146zm4.943 12.248V6.169H2.542v7.225h2.401zm-1.2-8.212c.837 0 1.358-.554 1.358-1.248-.015-.709-.52-1.248-1.342-1.248-.822 0-1.359.54-1.359 1.248 0 .694.521 1.248 1.327 1.248h.016zm4.908 8.212V9.359c0-.216.016-.432.08-.586.173-.431.568-.878 1.232-.878.869 0 1.216.662 1.216 1.634v3.865h2.401V9.25c0-2.22-1.184-3.252-2.764-3.252-1.274 0-1.845.7-2.165 1.193v.025h-.016a5.54 5.54 0 0 1 .016-.025V6.169h-2.4c.03.678 0 7.225 0 7.225h2.4z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 524,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                            className: \"my-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 531,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row align-items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-md-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted mb-0\",\n                                        children: [\n                                            \"\\xa9 Copyright \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"DealClosed,\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 58\n                                            }, undefined),\n                                            \" All Rights Reserved\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 534,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-md-6 text-end\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/us.png\",\n                                            alt: \"English\",\n                                            width: \"20\",\n                                            className: \"me-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/nl.png\",\n                                            alt: \"Dutch\",\n                                            width: \"20\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 537,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 533,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 477,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_c = HomePage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomePage);\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n"));

/***/ }),

/***/ "./node_modules/next/head.js":
/*!***********************************!*\
  !*** ./node_modules/next/head.js ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/shared/lib/head */ \"./node_modules/next/dist/shared/lib/head.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzIiwibWFwcGluZ3MiOiJBQUFBLGlIQUFrRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9oZWFkLmpzPzg4NDkiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3Qvc2hhcmVkL2xpYi9oZWFkJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/head.js\n"));

/***/ }),

/***/ "./node_modules/next/link.js":
/*!***********************************!*\
  !*** ./node_modules/next/link.js ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/client/link */ \"./node_modules/next/dist/client/link.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9saW5rLmpzIiwibWFwcGluZ3MiOiJBQUFBLHlHQUE4QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9saW5rLmpzPzc1YjMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3QvY2xpZW50L2xpbmsnKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/link.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5CFAST%5CFreelance%5CProject9_SalesPlatform%5CDealClosed%5Cfrontend%5Csrc%5Cpages%5Cindex.tsx&page=%2F!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);