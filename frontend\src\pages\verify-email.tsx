import Layout from '@/components/Layout';
import { authAPI } from '@/utils/api';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { FiCheckCircle, FiMail, FiXCircle } from 'react-icons/fi';

const VerifyEmailPage: React.FC = () => {
  const router = useRouter();
  const { token } = router.query;
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState<string>('');

  useEffect(() => {
    if (token && typeof token === 'string') {
      verifyEmail(token);
    }
  }, [token]);

  const verifyEmail = async (verificationToken: string) => {
    try {
      const response = await authAPI.verifyEmail(verificationToken);
      setStatus('success');
      setMessage(response.data.message || 'Email verified successfully!');
    } catch (error: any) {
      setStatus('error');
      setMessage(error.response?.data?.message || 'Email verification failed. The link may be invalid or expired.');
    }
  };

  const renderContent = () => {
    switch (status) {
      case 'loading':
        return (
          <div className="text-center">
            <div className="spinner-border text-primary mb-3" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <h3>Verifying your email...</h3>
            <p className="text-muted">Please wait while we verify your email address.</p>
          </div>
        );

      case 'success':
        return (
          <div className="text-center">
            <FiCheckCircle size={64} className="text-success mb-3" />
            <h3 className="text-success">Email Verified Successfully!</h3>
            <p className="text-muted mb-4">{message}</p>
            <div className="d-grid gap-2">
              <Link href="/login" className="btn btn-primary btn-lg">
                Continue to Login
              </Link>
              <Link href="/" className="btn btn-outline-secondary">
                Go to Homepage
              </Link>
            </div>
          </div>
        );

      case 'error':
        return (
          <div className="text-center">
            <FiXCircle size={64} className="text-danger mb-3" />
            <h3 className="text-danger">Verification Failed</h3>
            <p className="text-muted mb-4">{message}</p>
            <div className="alert alert-warning text-start">
              <FiMail className="me-2" />
              <strong>Need help?</strong>
              <ul className="mb-0 mt-2">
                <li>Check if the verification link is complete</li>
                <li>Make sure you're using the latest email we sent</li>
                <li>Verification links expire after 24 hours</li>
              </ul>
            </div>
            <div className="d-grid gap-2">
              <Link href="/register" className="btn btn-primary">
                Register Again
              </Link>
              <Link href="/login" className="btn btn-outline-primary">
                Try to Login
              </Link>
              <Link href="/" className="btn btn-outline-secondary">
                Go to Homepage
              </Link>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Layout title="Email Verification - DealClosed Partner">
      <div className="container py-5">
        <div className="row justify-content-center">
          <div className="col-md-6 col-lg-5">
            <div className="card shadow-sm border-0">
              <div className="card-body p-5">
                {renderContent()}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default VerifyEmailPage;
