"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_FiBriefcase_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FiBriefcase,FiUsers!=!react-icons/fi */ \"__barrel_optimize__?names=FiBriefcase,FiUsers!=!./node_modules/react-icons/fi/index.esm.js\");\n\n\n\n\n\nconst HomePage = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"DealClosed Partner - The platform where sales come together\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"At DealClosed, we connect businesses with freelance sales agents in a win-win situation. Businesses gain access to top sales agents, while agents have the freedom to work flexibly—anytime, anywhere.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"navbar navbar-expand-lg navbar-light bg-white shadow-sm py-3 fixed-top\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"navbar-brand\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"fw-bold text-success\",\n                                style: {\n                                    fontSize: \"1.7rem\"\n                                },\n                                children: \"DealClosed\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"navbar-toggler\",\n                            type: \"button\",\n                            \"data-bs-toggle\": \"collapse\",\n                            \"data-bs-target\": \"#navbarNav\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"navbar-toggler-icon\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"collapse navbar-collapse\",\n                            id: \"navbarNav\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"navbar-nav me-auto ms-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\",\n                                                className: \"nav-link text-success\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"At home\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 35,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/assignments\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Commands\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 38,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/freelancers\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Discover freelancers\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 41,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/companies\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Find company\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/courses\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Courses\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/ranking\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Rankings\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/blog\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Blogs\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"d-flex gap-2 align-items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/login\",\n                                            className: \"nav-link text-dark\",\n                                            style: {\n                                                fontWeight: \"500\",\n                                                fontSize: \"1.05rem\"\n                                            },\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/register\",\n                                            className: \"btn btn-success rounded-pill px-4 py-2\",\n                                            style: {\n                                                fontWeight: \"500\",\n                                                fontSize: \"15px\"\n                                            },\n                                            children: \"Get started\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"position-relative\",\n                style: {\n                    backgroundImage: \"url(/hero-bg.jpg)\",\n                    backgroundSize: \"cover\",\n                    backgroundPosition: \"center\",\n                    minHeight: \"100vh\",\n                    marginTop: \"80px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container h-100 d-flex align-items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row w-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-lg-8 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"display-3 fw-bold mb-4\",\n                                    style: {\n                                        fontSize: \"3.5rem\",\n                                        lineHeight: \"1.2\"\n                                    },\n                                    children: [\n                                        \"Welcome to Deal Closed,\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 40\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"2.8rem\"\n                                            },\n                                            children: \"the platform where sales come together\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"lead mb-4\",\n                                    style: {\n                                        fontSize: \"1.25rem\",\n                                        opacity: \"0.95\",\n                                        maxWidth: \"600px\"\n                                    },\n                                    children: \"At DealClosed, we connect companies with freelance sales agents in a win-win situation. Businesses get access to top sales agents, while agents have the freedom to work flexibly — anytime, anywhere.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn btn-lg px-5 py-3 rounded-pill\",\n                                    style: {\n                                        backgroundColor: \"transparent\",\n                                        border: \"2px solid white\",\n                                        color: \"white\",\n                                        fontSize: \"16px\",\n                                        fontWeight: \"400\",\n                                        transition: \"all 0.3s ease\"\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        const target = e.target;\n                                        target.style.backgroundColor = \"#28a745\";\n                                        target.style.borderColor = \"#28a745\";\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        const target = e.target;\n                                        target.style.backgroundColor = \"transparent\";\n                                        target.style.borderColor = \"white\";\n                                    },\n                                    onClick: ()=>window.location.href = \"/register\",\n                                    children: \"Get started\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-3\",\n                style: {\n                    backgroundColor: \"#f8f9fa\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                        className: \"mb-0 text-muted\",\n                        style: {\n                            fontSize: \"0.9rem\"\n                        },\n                        children: \"Connecting companies with freelance sales associates\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-lg-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"fw-bold mb-4\",\n                                    style: {\n                                        fontSize: \"2.5rem\",\n                                        color: \"#333\"\n                                    },\n                                    children: \"Connecting companies with freelance sales agents who deliver.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-3\",\n                                    style: {\n                                        fontSize: \"1.1rem\",\n                                        lineHeight: \"1.6\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"DealClosed\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" is the platform where companies and freelance sales agents meet. No expensive contracts or recruitment agencies – just collaboration based on performance.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-3\",\n                                    style: {\n                                        fontSize: \"1.1rem\",\n                                        lineHeight: \"1.6\"\n                                    },\n                                    children: \"Whether you're looking to close deals or find someone who can do it for you, DealClosed is the place to be.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        fontSize: \"1.1rem\",\n                                        lineHeight: \"1.6\",\n                                        color: \"#666\",\n                                        fontStyle: \"italic\"\n                                    },\n                                    children: \"Work flexibly. Earn fairly. Grow together.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row g-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"15px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"6px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"15px\",\n                                                borderBottomLeftRadius: \"15px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"48\",\n                                                        height: \"48\",\n                                                        fill: \"#28a745\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M7 14s-1 0-1-1 1-4 5-4 5 3 5 4-1 1-1 1H7zm4-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M5.216 14A2.238 2.238 0 0 1 5 13c0-1.355.68-2.75 1.936-3.72A6.325 6.325 0 0 0 5 9c-4 0-5 3-5 4s1 1 1 1h4.216z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M4.5 8a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"card-title mb-3\",\n                                                    children: \"Benefits for freelance sales associates\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-unstyled text-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-2\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Work wherever you want\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 44\n                                                                }, undefined),\n                                                                \"—at home, in a caf\\xe9, or on the go—you choose when and where you work.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-2\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Earn attractive commissions:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 44\n                                                                }, undefined),\n                                                                \" Get a fair fee per deal closed. Your success is in your hands.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-2\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Diverse opportunities:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 174,\n                                                                    columnNumber: 44\n                                                                }, undefined),\n                                                                \" discover a wide range of projects and companies that match your skills and interests\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"15px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"6px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"15px\",\n                                                borderBottomLeftRadius: \"15px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"48\",\n                                                        height: \"48\",\n                                                        fill: \"#28a745\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8.5 5.5a.5.5 0 0 0-1 0v3.362l-1.429 2.38a.5.5 0 1 0 .858.515l1.5-2.5A.5.5 0 0 0 8.5 9V5.5z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M6.5 0C3.48 0 1 2.48 1 5.5c0 3.78 3.4 6.86 8.55 11.54l1.1.94.9-.94C16.6 12.36 20 9.28 20 5.5 20 2.48 17.52 0 14.5 0S9 2.48 9 5.5C9 2.48 6.52 0 3.5 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"card-title mb-3\",\n                                                    children: \"Benefits for companies\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-unstyled text-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-2\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"More revenue, less risk\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 199,\n                                                                    columnNumber: 44\n                                                                }, undefined),\n                                                                \"—only pay for results. No fixed salaries or recruitment costs. Get clear results per closed.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-2\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Access to skilled sales reps:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 200,\n                                                                    columnNumber: 44\n                                                                }, undefined),\n                                                                \" Connect with motivated freelancers who are ready to grow your business.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-2\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Quick and easy collaboration:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 201,\n                                                                    columnNumber: 44\n                                                                }, undefined),\n                                                                \" Post your brief, select the right sales agent, and let the deals flow.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row align-items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"fw-bold mb-4\",\n                                        style: {\n                                            fontSize: \"2.5rem\",\n                                            color: \"#333\"\n                                        },\n                                        children: \"Why we do what we do\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted mb-4\",\n                                        style: {\n                                            fontSize: \"1rem\",\n                                            lineHeight: \"1.6\",\n                                            fontStyle: \"italic\"\n                                        },\n                                        children: \"At DealClosed, we believe in solving real sales challenges with simple, effective solutions. Our platform has been developed in response to a clear need in the market:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"d-flex align-items-start mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"me-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-success\",\n                                                        style: {\n                                                            fontSize: \"1.2rem\"\n                                                        },\n                                                        children: \"✓\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"mb-2\",\n                                                            style: {\n                                                                fontSize: \"1.1rem\",\n                                                                fontWeight: \"600\"\n                                                            },\n                                                            children: \"For businesses:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            style: {\n                                                                fontSize: \"0.95rem\",\n                                                                lineHeight: \"1.6\",\n                                                                margin: \"0\"\n                                                            },\n                                                            children: \"Many companies struggle to find reliable sales talent without long-term contracts or high hiring costs. They need flexible, results-driven solutions to grow their revenue risk-free.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"d-flex align-items-start mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"me-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-success\",\n                                                        style: {\n                                                            fontSize: \"1.2rem\"\n                                                        },\n                                                        children: \"✓\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"mb-2\",\n                                                            style: {\n                                                                fontSize: \"1.1rem\",\n                                                                fontWeight: \"600\"\n                                                            },\n                                                            children: \"For sales agents:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            style: {\n                                                                fontSize: \"0.95rem\",\n                                                                lineHeight: \"1.6\",\n                                                                margin: \"0\"\n                                                            },\n                                                            children: \"Talented people often lack the freedom to work on their own terms or find opportunities that match their unique skill sets. Many are stuck in rigid working models that limit their potential. We give them the opportunity to work freely, wherever they are, and achieve success based on performance.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"btn btn-success rounded-pill px-4 py-2\",\n                                        style: {\n                                            fontSize: \"0.9rem\",\n                                            fontWeight: \"400\"\n                                        },\n                                        onClick: ()=>window.location.href = \"/about\",\n                                        children: \"Read more →\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"rounded shadow\",\n                                    style: {\n                                        height: \"400px\",\n                                        backgroundImage: \"url(/about.jpg)\",\n                                        backgroundSize: \"cover\",\n                                        backgroundPosition: \"center\",\n                                        backgroundRepeat: \"no-repeat\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5 bg-light\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row g-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"15px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"6px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"15px\",\n                                                borderBottomLeftRadius: \"15px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4\",\n                                            style: {\n                                                paddingLeft: \"2rem\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"d-flex align-items-center mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-success bg-opacity-10 rounded-circle p-3 me-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBriefcase_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiUsers, {\n                                                                className: \"text-success\",\n                                                                size: 24\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"mb-0 fw-bold\",\n                                                            style: {\n                                                                fontSize: \"1.3rem\",\n                                                                color: \"#666\"\n                                                            },\n                                                            children: \"Benefits for freelance sales associates\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-unstyled\",\n                                                    style: {\n                                                        fontSize: \"0.9rem\",\n                                                        lineHeight: \"1.6\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Work wherever you want\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"—at home, in a caf\\xe9, or on the go—you choose when and where you work.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Earn attractive commissions:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 306,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \" Get a fair fee per deal closed. Your success is in your hands.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Diverse opportunities:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \" discover a wide range of projects and companies that match your skills and interests\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"15px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"6px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"15px\",\n                                                borderBottomLeftRadius: \"15px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4\",\n                                            style: {\n                                                paddingLeft: \"2rem\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"d-flex align-items-center mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-success bg-opacity-10 rounded-circle p-3 me-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBriefcase_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiBriefcase, {\n                                                                className: \"text-success\",\n                                                                size: 24\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"mb-0 fw-bold\",\n                                                            style: {\n                                                                fontSize: \"1.3rem\",\n                                                                color: \"#666\"\n                                                            },\n                                                            children: \"Benefits for companies\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-unstyled\",\n                                                    style: {\n                                                        fontSize: \"0.9rem\",\n                                                        lineHeight: \"1.6\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"More revenue, less risk\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 340,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"—only pay for results. No fixed salaries or recruitment costs – just a clear commission per deal closed.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Access to skilled sales reps:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \" Connect with motivated freelancers who are ready to grow your business.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Quick and easy collaboration:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \" Post your brief, select the right sales agent, and let the deals flow.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row g-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm text-white\",\n                                    style: {\n                                        backgroundColor: \"#28a745\",\n                                        borderRadius: \"15px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card-body p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"fw-bold mb-4\",\n                                                children: \"Why should you choose us?\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mb-4\",\n                                                children: \"To bridge this gap, we founded DealClosed. By linking companies with motivated freelance sales staff, we offer a win-win situation: companies achieve results without unnecessary overhead and sales employees get the flexibility to work how, when and where they want.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn px-4 py-2 rounded-pill\",\n                                                style: {\n                                                    backgroundColor: \"#1e7e34\",\n                                                    border: \"none\",\n                                                    color: \"white\",\n                                                    fontSize: \"0.9rem\",\n                                                    fontWeight: \"400\",\n                                                    transition: \"all 0.3s ease\"\n                                                },\n                                                onMouseEnter: (e)=>{\n                                                    const target = e.target;\n                                                    target.style.backgroundColor = \"white\";\n                                                    target.style.color = \"#28a745\";\n                                                },\n                                                onMouseLeave: (e)=>{\n                                                    const target = e.target;\n                                                    target.style.backgroundColor = \"#1e7e34\";\n                                                    target.style.color = \"white\";\n                                                },\n                                                onClick: ()=>window.location.href = \"/about\",\n                                                children: \"Learn more →\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"15px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"6px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"15px\",\n                                                borderBottomLeftRadius: \"15px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"32\",\n                                                        height: \"32\",\n                                                        fill: \"#28a745\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M8 1a2.5 2.5 0 0 1 2.5 2.5V4h-5v-.5A2.5 2.5 0 0 1 8 1zm3.5 3v-.5a3.5 3.5 0 1 0-7 0V4H1v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4h-3.5zM2 5h12v9a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V5z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold mb-3\",\n                                                    children: \"Transparency\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted\",\n                                                    style: {\n                                                        fontSize: \"0.85rem\"\n                                                    },\n                                                    children: \"With a simple 15% commission on successful deals, we guarantee fairness and clarity in every transaction.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"15px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"6px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"15px\",\n                                                borderBottomLeftRadius: \"15px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"32\",\n                                                        height: \"32\",\n                                                        fill: \"#28a745\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M2.5 3A1.5 1.5 0 0 0 1 4.5v.793c.026.009.051.02.076.032L7.674 8.51c.206.1.446.1.652 0l6.598-3.185A.755.755 0 0 1 15 5.293V4.5A1.5 1.5 0 0 0 13.5 3h-11Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M15 6.954 8.978 9.86a2.25 2.25 0 0 1-1.956 0L1 6.954V11.5A1.5 1.5 0 0 0 2.5 13h11a1.5 1.5 0 0 0 1.5-1.5V6.954Z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 440,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold mb-3\",\n                                                    children: \"Flexibility\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted\",\n                                                    style: {\n                                                        fontSize: \"0.85rem\"\n                                                    },\n                                                    children: \"We embrace the future of work and offer sellers and companies freedom and choice.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"15px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"6px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"15px\",\n                                                borderBottomLeftRadius: \"15px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4 text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"32\",\n                                                        height: \"32\",\n                                                        fill: \"#28a745\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8.5 4.5a.5.5 0 0 0-1 0v3h-3a.5.5 0 0 0 0 1h3v3a.5.5 0 0 0 1 0v-3h3a.5.5 0 0 0 0-1h-3v-3z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold mb-3\",\n                                                    children: \"Focus on results\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted\",\n                                                    style: {\n                                                        fontSize: \"0.85rem\"\n                                                    },\n                                                    children: \"We believe in rewarding results, not effort. We create a performance-oriented culture for everyone involved.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 360,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5 bg-light\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"fw-bold mb-4\",\n                            children: \"Assignments\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                            className: \"text-muted mb-4\",\n                            children: \"Latest Assignments\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 490,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card border-0 shadow-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-img-top bg-primary d-flex align-items-center justify-content-center\",\n                                            style: {\n                                                height: \"200px\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"60\",\n                                                        height: \"60\",\n                                                        fill: \"white\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854V1.146zM1.175 1a.146.146 0 0 0-.146.146v13.708c0 .08.066.146.146.146h13.65a.146.146 0 0 0 .146-.146V1.146a.146.146 0 0 0-.146-.146H1.175z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 498,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                        className: \"mt-2 mb-0\",\n                                                        children: \"Information Technology\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"badge bg-primary mb-2\",\n                                                    children: \"Information Technology\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-success fs-4 fw-bold\",\n                                                    children: \"$\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/assignments/5\",\n                                                        className: \"text-decoration-none text-dark\",\n                                                        children: \"Web application development\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted mb-2\",\n                                                    children: \"Test Assignment\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Payment Type:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        \" Hourly ($17.00)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 513,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mb-3\",\n                                                    children: \"Applications: 1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/login\",\n                                                    className: \"btn btn-success w-100\",\n                                                    children: \"Take This Deal\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 488,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 487,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white py-5 border-top\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-lg-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                            className: \"fw-bold text-success mb-4\",\n                                            children: \"DealClosed\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted mb-2\",\n                                            children: \"Amsterdam, The Netherlands\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Email:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 46\n                                                }, undefined),\n                                                \" <EMAIL>\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 529,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-lg-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                            className: \"fw-bold mb-3 text-success\",\n                                            children: \"Useful links\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"list-unstyled\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Home\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/about\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"About us\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/assignments\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Assignments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-lg-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                            className: \"fw-bold mb-3 text-success\",\n                                            children: \"Other links\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"list-unstyled\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/contact\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Contact Us\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/terms\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Terms of service\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/privacy\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Privacy policy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-lg-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                            className: \"fw-bold mb-3 text-success\",\n                                            children: \"Follow us\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 554,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"d-flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"btn btn-outline-secondary btn-sm rounded-circle\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"btn btn-outline-secondary btn-sm rounded-circle\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"btn btn-outline-secondary btn-sm rounded-circle\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 568,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 567,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"btn btn-outline-secondary btn-sm rounded-circle\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854V1.146zm4.943 12.248V6.169H2.542v7.225h2.401zm-1.2-8.212c.837 0 1.358-.554 1.358-1.248-.015-.709-.52-1.248-1.342-1.248-.822 0-1.359.54-1.359 1.248 0 .694.521 1.248 1.327 1.248h.016zm4.908 8.212V9.359c0-.216.016-.432.08-.586.173-.431.568-.878 1.232-.878.869 0 1.216.662 1.216 1.634v3.865h2.401V9.25c0-2.22-1.184-3.252-2.764-3.252-1.274 0-1.845.7-2.165 1.193v.025h-.016a5.54 5.54 0 0 1 .016-.025V6.169h-2.4c.03.678 0 7.225 0 7.225h2.4z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 572,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                            className: \"my-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 580,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row align-items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-md-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted mb-0\",\n                                        children: [\n                                            \"\\xa9 Copyright \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"DealClosed,\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 58\n                                            }, undefined),\n                                            \" All Rights Reserved\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 583,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-md-6 text-end\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/us.png\",\n                                            alt: \"English\",\n                                            width: \"20\",\n                                            className: \"me-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/nl.png\",\n                                            alt: \"Dutch\",\n                                            width: \"20\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 588,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 586,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 582,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 527,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 526,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_c = HomePage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomePage);\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n"));

/***/ })

});