{"name": "dealclosed-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@restart/hooks": "^0.6.2", "@restart/ui": "^1.9.4", "axios": "^1.6.2", "bootstrap": "^5.3.2", "critters": "^0.0.20", "js-cookie": "^3.0.5", "next": "14.0.4", "next-auth": "^4.24.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-icons": "^4.12.0", "swr": "^2.2.4", "yup": "^1.4.0"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "20.19.9", "@types/react": "18.3.23", "@types/react-dom": "18.3.7", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "typescript": "5.8.3"}}