import { authAPI } from '@/utils/api';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';

interface RegisterFormData {
  email: string;
  password: string;
  confirmPassword: string;
  role: 'company' | 'freelancer';
  firstName?: string;
  lastName?: string;
  companyName?: string;
  agreeToTerms: boolean;
}

const RegisterPage: React.FC = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm<RegisterFormData>({
    defaultValues: {
      role: 'company'
    }
  });

  const watchRole = watch('role');
  const watchPassword = watch('password');

  const onSubmit = async (data: RegisterFormData) => {
    if (data.password !== data.confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    setIsLoading(true);
    try {
      const response = await authAPI.register(data);
      
      if (response.success) {
        toast.success('Registration successful! Please check your email to verify your account.');
        router.push('/login');
      } else {
        toast.error(response.message || 'Registration failed');
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || 'Registration failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>Register - DealClosed Partner</title>
        <meta name="description" content="Create your DealClosed Partner account" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      {/* Navigation - Same as home page */}
      <nav className="navbar navbar-expand-lg navbar-light bg-white shadow-sm py-3 fixed-top">
        <div className="container">
          <Link href="/" className="navbar-brand d-flex align-items-center">
            <img src="/assets/img/default.png" alt="DealClosed Logo" width="40" height="40" className="me-2" />
            <span className="fw-bold text-success" style={{ fontSize: '1.7rem' }}>DealClosed</span>
          </Link>

          <button
            className="navbar-toggler"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#navbarNav"
          >
            <span className="navbar-toggler-icon"></span>
          </button>

          <div className="collapse navbar-collapse" id="navbarNav">
            <ul className="navbar-nav me-auto ms-4">
              <li className="nav-item">
                <Link href="/" className="nav-link text-dark" style={{ fontWeight: '500', fontSize: '1.05rem' }}>At home</Link>
              </li>
              <li className="nav-item">
                <Link href="/assignments" className="nav-link text-dark" style={{ fontWeight: '500', fontSize: '1.05rem' }}>Commands</Link>
              </li>
              <li className="nav-item">
                <Link href="/freelancers" className="nav-link text-dark" style={{ fontWeight: '500', fontSize: '1.05rem' }}>Discover freelancers</Link>
              </li>
              <li className="nav-item">
                <Link href="/companies" className="nav-link text-dark" style={{ fontWeight: '500', fontSize: '1.05rem' }}>Find company</Link>
              </li>
              <li className="nav-item">
                <Link href="/courses" className="nav-link text-dark" style={{ fontWeight: '500', fontSize: '1.05rem' }}>Courses</Link>
              </li>
              <li className="nav-item">
                <Link href="/ranking" className="nav-link text-dark" style={{ fontWeight: '500', fontSize: '1.05rem' }}>Rankings</Link>
              </li>
              <li className="nav-item">
                <Link href="/blog" className="nav-link text-dark" style={{ fontWeight: '500', fontSize: '1.05rem' }}>Blogs</Link>
              </li>
            </ul>

            <div className="d-flex gap-2 align-items-center">
              <Link href="/login" className="nav-link text-dark" style={{ fontWeight: '500', fontSize: '1.05rem' }}>
                Login
              </Link>
              <Link href="/register" className="nav-link text-success" style={{ fontWeight: '500', fontSize: '1.05rem' }}>
                Get started
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Register Section - Same layout as login */}
      <div className="container-fluid p-0" style={{ marginTop: '80px' }}>
        <div className="row g-0">
          {/* Left side - Image */}
          <div className="col-lg-6 d-none d-lg-block">
            <div
              className="h-100 d-flex align-items-center justify-content-center"
              style={{
                minHeight: 'calc(100vh - 80px)',
                backgroundImage: 'url(/assets/img/default.png)',
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat'
              }}
            ></div>
          </div>

          {/* Right side - Register Form */}
          <div className="col-lg-6">
            <div className="d-flex align-items-center justify-content-center p-5" style={{ minHeight: 'calc(100vh - 80px)' }}>
              <div className="w-100" style={{ maxWidth: '400px' }}>
                <h2 className="fw-bold mb-4">Get Started!</h2>

                <form onSubmit={handleSubmit(onSubmit)}>
                  {/* User Type */}
                  <div className="mb-3">
                    <label className="form-label">I want to register as</label>
                    <div>
                      <div className="form-check">
                        <input
                          className="form-check-input"
                          type="radio"
                          value="company"
                          id="company"
                          {...register('role')}
                          defaultChecked
                        />
                        <label className="form-check-label" htmlFor="company">
                          Company
                        </label>
                      </div>
                      <div className="form-check">
                        <input
                          className="form-check-input"
                          type="radio"
                          value="freelancer"
                          id="freelancer"
                          {...register('role')}
                        />
                        <label className="form-check-label" htmlFor="freelancer">
                          Sales professional
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* Conditional fields based on role */}
                  {watchRole === 'freelancer' ? (
                    <>
                      <div className="row">
                        <div className="col-6">
                          <div className="mb-3">
                            <label htmlFor="firstName" className="form-label">First Name</label>
                            <input
                              type="text"
                              className={`form-control ${errors.firstName ? 'is-invalid' : ''}`}
                              id="firstName"
                              {...register('firstName', { required: 'First name is required' })}
                            />
                            {errors.firstName && (
                              <div className="invalid-feedback">{errors.firstName.message}</div>
                            )}
                          </div>
                        </div>
                        <div className="col-6">
                          <div className="mb-3">
                            <label htmlFor="lastName" className="form-label">Last Name</label>
                            <input
                              type="text"
                              className={`form-control ${errors.lastName ? 'is-invalid' : ''}`}
                              id="lastName"
                              {...register('lastName', { required: 'Last name is required' })}
                            />
                            {errors.lastName && (
                              <div className="invalid-feedback">{errors.lastName.message}</div>
                            )}
                          </div>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="mb-3">
                      <label htmlFor="companyName" className="form-label">Company Name</label>
                      <input
                        type="text"
                        className={`form-control ${errors.companyName ? 'is-invalid' : ''}`}
                        id="companyName"
                        {...register('companyName', { required: 'Company name is required' })}
                      />
                      {errors.companyName && (
                        <div className="invalid-feedback">{errors.companyName.message}</div>
                      )}
                    </div>
                  )}

                  {/* Email */}
                  <div className="mb-3">
                    <label htmlFor="email" className="form-label">E-mail</label>
                    <input
                      type="email"
                      className={`form-control ${errors.email ? 'is-invalid' : ''}`}
                      id="email"
                      {...register('email', { 
                        required: 'Email is required',
                        pattern: {
                          value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                          message: 'Invalid email address'
                        }
                      })}
                    />
                    {errors.email && (
                      <div className="invalid-feedback">{errors.email.message}</div>
                    )}
                  </div>

                  {/* Password */}
                  <div className="mb-3">
                    <label htmlFor="password" className="form-label">Password</label>
                    <div className="position-relative">
                      <input
                        type={showPassword ? 'text' : 'password'}
                        className={`form-control ${errors.password ? 'is-invalid' : ''}`}
                        id="password"
                        {...register('password', { 
                          required: 'Password is required',
                          minLength: {
                            value: 6,
                            message: 'Password must be at least 6 characters'
                          }
                        })}
                      />
                      <button
                        type="button"
                        className="btn btn-link position-absolute end-0 top-50 translate-middle-y"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? '👁️' : '👁️‍🗨️'}
                      </button>
                    </div>
                    {errors.password && (
                      <div className="invalid-feedback d-block">{errors.password.message}</div>
                    )}
                  </div>

                  {/* Confirm Password */}
                  <div className="mb-3">
                    <label htmlFor="confirmPassword" className="form-label">Confirm Password</label>
                    <div className="position-relative">
                      <input
                        type={showConfirmPassword ? 'text' : 'password'}
                        className={`form-control ${errors.confirmPassword ? 'is-invalid' : ''}`}
                        id="confirmPassword"
                        {...register('confirmPassword', { 
                          required: 'Please confirm your password',
                          validate: value => value === watchPassword || 'Passwords do not match'
                        })}
                      />
                      <button
                        type="button"
                        className="btn btn-link position-absolute end-0 top-50 translate-middle-y"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      >
                        {showConfirmPassword ? '👁️' : '👁️‍🗨️'}
                      </button>
                    </div>
                    {errors.confirmPassword && (
                      <div className="invalid-feedback d-block">{errors.confirmPassword.message}</div>
                    )}
                  </div>

                  {/* Terms Agreement */}
                  <div className="mb-4">
                    <div className="form-check">
                      <input
                        className={`form-check-input ${errors.agreeToTerms ? 'is-invalid' : ''}`}
                        type="checkbox"
                        id="agreeToTerms"
                        {...register('agreeToTerms', { required: 'You must agree to the terms' })}
                      />
                      <label className="form-check-label" htmlFor="agreeToTerms">
                        I agree to the <Link href="/terms" className="text-success">Terms of Service</Link> and <Link href="/privacy" className="text-success">Privacy Policy</Link>
                      </label>
                      {errors.agreeToTerms && (
                        <div className="invalid-feedback d-block">{errors.agreeToTerms.message}</div>
                      )}
                    </div>
                  </div>

                  {/* Register Button */}
                  <button
                    type="submit"
                    className="btn btn-success w-100 py-2 mb-3"
                    disabled={isLoading}
                  >
                    {isLoading ? 'Creating Account...' : 'Create Account'}
                  </button>

                  {/* Links */}
                  <div className="text-center">
                    <Link href="/login" className="text-success text-decoration-none">
                      Already have an account? Login here
                    </Link>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer - Exact match to dealclosedpartner.nl */}
      <footer className="bg-white py-5 border-top">
        <div className="container">
          <div className="row">
            <div className="col-lg-3">
              <h5 className="fw-bold text-success mb-4">DealClosed</h5>
              <p className="text-muted mb-2">Amsterdam, The Netherlands</p>
              <p className="text-muted mb-4"><strong>Email:</strong> <EMAIL></p>
            </div>

            <div className="col-lg-3">
              <h6 className="fw-bold mb-3">Useful Links</h6>
              <ul className="list-unstyled">
                <li className="mb-2"><Link href="/" className="text-muted text-decoration-none">Home</Link></li>
                <li className="mb-2"><Link href="/about" className="text-muted text-decoration-none">About us</Link></li>
                <li className="mb-2"><Link href="/assignments" className="text-muted text-decoration-none">Assignments</Link></li>
              </ul>
            </div>

            <div className="col-lg-3">
              <h6 className="fw-bold mb-3">Other Links</h6>
              <ul className="list-unstyled">
                <li className="mb-2"><Link href="/contact" className="text-muted text-decoration-none">Contact Us</Link></li>
                <li className="mb-2"><Link href="/terms" className="text-muted text-decoration-none">Terms of service</Link></li>
                <li className="mb-2"><Link href="/privacy" className="text-muted text-decoration-none">Privacy policy</Link></li>
              </ul>
            </div>

            <div className="col-lg-3">
              <h6 className="fw-bold mb-3">Follow Us</h6>
              <div className="d-flex gap-2">
                <a href="#" className="btn btn-outline-secondary btn-sm rounded-circle">
                  <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z"/>
                  </svg>
                </a>
                <a href="#" className="btn btn-outline-secondary btn-sm rounded-circle">
                  <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z"/>
                  </svg>
                </a>
                <a href="#" className="btn btn-outline-secondary btn-sm rounded-circle">
                  <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z"/>
                  </svg>
                </a>
                <a href="#" className="btn btn-outline-secondary btn-sm rounded-circle">
                  <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854V1.146zm4.943 12.248V6.169H2.542v7.225h2.401zm-1.2-8.212c.837 0 1.358-.554 1.358-1.248-.015-.709-.52-1.248-1.342-1.248-.822 0-1.359.54-1.359 1.248 0 .694.521 1.248 1.327 1.248h.016zm4.908 8.212V9.359c0-.216.016-.432.08-.586.173-.431.568-.878 1.232-.878.869 0 1.216.662 1.216 1.634v3.865h2.401V9.25c0-2.22-1.184-3.252-2.764-3.252-1.274 0-1.845.7-2.165 1.193v.025h-.016a5.54 5.54 0 0 1 .016-.025V6.169h-2.4c.03.678 0 7.225 0 7.225h2.4z"/>
                  </svg>
                </a>
              </div>
            </div>
          </div>

          <hr className="my-4" />

          <div className="row align-items-center">
            <div className="col-md-6">
              <p className="text-muted mb-0">© Copyright <strong>DealClosed,</strong> All Rights Reserved</p>
            </div>
            <div className="col-md-6 text-end">
              <img src="/us.png" alt="English" width="20" className="me-2" />
              <img src="/nl.png" alt="Dutch" width="20" />
            </div>
          </div>
        </div>
      </footer>
    </>
  );
};

export default RegisterPage;
