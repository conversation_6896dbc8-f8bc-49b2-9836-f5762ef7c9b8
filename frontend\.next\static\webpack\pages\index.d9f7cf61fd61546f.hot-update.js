"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_FiBriefcase_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FiBriefcase,FiUsers!=!react-icons/fi */ \"__barrel_optimize__?names=FiBriefcase,FiUsers!=!./node_modules/react-icons/fi/index.esm.js\");\n\n\n\n\n\nconst HomePage = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"DealClosed Partner - The platform where sales come together\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"At DealClosed, we connect businesses with freelance sales agents in a win-win situation. Businesses gain access to top sales agents, while agents have the freedom to work flexibly—anytime, anywhere.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"navbar navbar-expand-lg navbar-light bg-white shadow-sm py-3 fixed-top\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"navbar-brand d-flex align-items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"me-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"32\",\n                                        height: \"32\",\n                                        viewBox: \"0 0 100 100\",\n                                        fill: \"none\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                cx: \"50\",\n                                                cy: \"50\",\n                                                r: \"45\",\n                                                fill: \"#28a745\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 22,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M30 50L45 65L70 35\",\n                                                stroke: \"white\",\n                                                strokeWidth: \"6\",\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 23,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 21,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"fw-bold text-success\",\n                                    style: {\n                                        fontSize: \"1.7rem\"\n                                    },\n                                    children: \"DealClosed\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"navbar-toggler\",\n                            type: \"button\",\n                            \"data-bs-toggle\": \"collapse\",\n                            \"data-bs-target\": \"#navbarNav\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"navbar-toggler-icon\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"collapse navbar-collapse\",\n                            id: \"navbarNav\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"navbar-nav me-auto ms-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\",\n                                                className: \"nav-link text-success\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"At home\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 41,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/assignments\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Commands\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/freelancers\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Discover freelancers\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/companies\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Find company\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/courses\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Courses\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/ranking\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Rankings\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"nav-item\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/blog\",\n                                                className: \"nav-link text-dark\",\n                                                style: {\n                                                    fontWeight: \"500\",\n                                                    fontSize: \"1.05rem\"\n                                                },\n                                                children: \"Blogs\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"d-flex gap-2 align-items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/login\",\n                                            className: \"nav-link text-dark\",\n                                            style: {\n                                                fontWeight: \"500\",\n                                                fontSize: \"1.05rem\"\n                                            },\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/register\",\n                                            className: \"btn btn-success rounded-pill px-4 py-2\",\n                                            style: {\n                                                fontWeight: \"500\",\n                                                fontSize: \"15px\"\n                                            },\n                                            children: \"Get started\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"position-relative\",\n                style: {\n                    backgroundImage: \"url(/hero-bg.jpg)\",\n                    backgroundSize: \"cover\",\n                    backgroundPosition: \"center\",\n                    minHeight: \"100vh\",\n                    marginTop: \"80px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container h-100 d-flex align-items-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row w-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-lg-8 text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"display-3 fw-bold mb-4\",\n                                    style: {\n                                        fontSize: \"3.5rem\",\n                                        lineHeight: \"1.2\"\n                                    },\n                                    children: [\n                                        \"Welcome to Deal Closed,\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 40\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: \"2.8rem\"\n                                            },\n                                            children: \"the platform where sales come together\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"lead mb-4\",\n                                    style: {\n                                        fontSize: \"1.25rem\",\n                                        opacity: \"0.95\",\n                                        maxWidth: \"600px\"\n                                    },\n                                    children: \"At DealClosed, we connect companies with freelance sales agents in a win-win situation. Businesses get access to top sales agents, while agents have the freedom to work flexibly — anytime, anywhere.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn btn-lg px-5 py-3 rounded-pill\",\n                                    style: {\n                                        backgroundColor: \"transparent\",\n                                        border: \"2px solid white\",\n                                        color: \"white\",\n                                        fontSize: \"16px\",\n                                        fontWeight: \"400\",\n                                        transition: \"all 0.3s ease\"\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        const target = e.target;\n                                        target.style.backgroundColor = \"#28a745\";\n                                        target.style.borderColor = \"#28a745\";\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        const target = e.target;\n                                        target.style.backgroundColor = \"transparent\";\n                                        target.style.borderColor = \"white\";\n                                    },\n                                    onClick: ()=>window.location.href = \"/register\",\n                                    children: \"Get started\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-3\",\n                style: {\n                    backgroundColor: \"#f8f9fa\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                        className: \"mb-0 text-muted\",\n                        style: {\n                            fontSize: \"0.9rem\"\n                        },\n                        children: \"Connecting companies with freelance sales associates\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-lg-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"fw-bold mb-4\",\n                                    style: {\n                                        fontSize: \"2.5rem\",\n                                        color: \"#333\"\n                                    },\n                                    children: \"Connecting companies with freelance sales agents who deliver.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-3\",\n                                    style: {\n                                        fontSize: \"1.1rem\",\n                                        lineHeight: \"1.6\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"DealClosed\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \" is the platform where companies and freelance sales agents meet. No expensive contracts or recruitment agencies – just collaboration based on performance.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-3\",\n                                    style: {\n                                        fontSize: \"1.1rem\",\n                                        lineHeight: \"1.6\"\n                                    },\n                                    children: \"Whether you're looking to close deals or find someone who can do it for you, DealClosed is the place to be.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        fontSize: \"1.1rem\",\n                                        lineHeight: \"1.6\",\n                                        color: \"#666\",\n                                        fontStyle: \"italic\"\n                                    },\n                                    children: \"Work flexibly. Earn fairly. Grow together.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5 bg-light\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row g-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"20px\",\n                                        minHeight: \"350px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"8px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"20px\",\n                                                borderBottomLeftRadius: \"20px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-5\",\n                                            style: {\n                                                paddingLeft: \"3rem\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center\",\n                                                        style: {\n                                                            width: \"80px\",\n                                                            height: \"80px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"40\",\n                                                            height: \"40\",\n                                                            fill: \"#28a745\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M7 14s-1 0-1-1 1-4 5-4 5 3 5 4-1 1-1 1H7zm4-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M5.216 14A2.238 2.238 0 0 1 5 13c0-1.355.68-2.75 1.936-3.72A6.325 6.325 0 0 0 5 9c-4 0-5 3-5 4s1 1 1 1h4.216z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M4.5 8a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 174,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-center fw-bold mb-4\",\n                                                    style: {\n                                                        fontSize: \"1.4rem\",\n                                                        color: \"#666\"\n                                                    },\n                                                    children: \"Benefits for freelance sales associates\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-unstyled\",\n                                                    style: {\n                                                        fontSize: \"1rem\",\n                                                        lineHeight: \"1.8\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Work wherever you want\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 181,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \"—at home, in a caf\\xe9, or on the go—you choose when and where you work.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Earn attractive commissions:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 185,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" Get a fair fee per deal closed. Your success is in your hands.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Diverse opportunities:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 189,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" discover a wide range of projects and companies that match your skills and interests\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"20px\",\n                                        minHeight: \"350px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"8px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"20px\",\n                                                borderBottomLeftRadius: \"20px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-5\",\n                                            style: {\n                                                paddingLeft: \"3rem\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center\",\n                                                        style: {\n                                                            width: \"80px\",\n                                                            height: \"80px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"40\",\n                                                            height: \"40\",\n                                                            fill: \"#28a745\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 211,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 212,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-center fw-bold mb-4\",\n                                                    style: {\n                                                        fontSize: \"1.4rem\",\n                                                        color: \"#666\"\n                                                    },\n                                                    children: \"Benefits for companies\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-unstyled\",\n                                                    style: {\n                                                        fontSize: \"1rem\",\n                                                        lineHeight: \"1.8\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"More revenue, less risk\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \"—only pay for results. No fixed salaries or recruitment costs – just a clear commission per deal closed.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Access to skilled sales reps:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 223,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" Connect with motivated freelancers who are ready to grow your business.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                \"• \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Quick and easy collaboration:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                \" Post your brief, select the right sales agent, and let the deals flow.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row align-items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"fw-bold mb-4\",\n                                        style: {\n                                            fontSize: \"2.5rem\",\n                                            color: \"#333\"\n                                        },\n                                        children: \"Why we do what we do\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted mb-4\",\n                                        style: {\n                                            fontSize: \"1rem\",\n                                            lineHeight: \"1.6\",\n                                            fontStyle: \"italic\"\n                                        },\n                                        children: \"At DealClosed, we believe in solving real sales challenges with simple, effective solutions. Our platform has been developed in response to a clear need in the market:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"d-flex align-items-start mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"me-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-success\",\n                                                        style: {\n                                                            fontSize: \"1.2rem\"\n                                                        },\n                                                        children: \"✓\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"mb-2\",\n                                                            style: {\n                                                                fontSize: \"1.1rem\",\n                                                                fontWeight: \"600\"\n                                                            },\n                                                            children: \"For businesses:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            style: {\n                                                                fontSize: \"0.95rem\",\n                                                                lineHeight: \"1.6\",\n                                                                margin: \"0\"\n                                                            },\n                                                            children: \"Many companies struggle to find reliable sales talent without long-term contracts or high hiring costs. They need flexible, results-driven solutions to grow their revenue risk-free.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"d-flex align-items-start mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"me-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-success\",\n                                                        style: {\n                                                            fontSize: \"1.2rem\"\n                                                        },\n                                                        children: \"✓\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"mb-2\",\n                                                            style: {\n                                                                fontSize: \"1.1rem\",\n                                                                fontWeight: \"600\"\n                                                            },\n                                                            children: \"For sales agents:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            style: {\n                                                                fontSize: \"0.95rem\",\n                                                                lineHeight: \"1.6\",\n                                                                margin: \"0\"\n                                                            },\n                                                            children: \"Talented people often lack the freedom to work on their own terms or find opportunities that match their unique skill sets. Many are stuck in rigid working models that limit their potential. We give them the opportunity to work freely, wherever they are, and achieve success based on performance.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"btn btn-success rounded-pill px-4 py-2\",\n                                        style: {\n                                            fontSize: \"0.9rem\",\n                                            fontWeight: \"400\"\n                                        },\n                                        onClick: ()=>window.location.href = \"/about\",\n                                        children: \"Read more →\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"rounded shadow\",\n                                    style: {\n                                        height: \"400px\",\n                                        backgroundImage: \"url(/about.jpg)\",\n                                        backgroundSize: \"cover\",\n                                        backgroundPosition: \"center\",\n                                        backgroundRepeat: \"no-repeat\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5 bg-light\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row g-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"15px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"6px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"15px\",\n                                                borderBottomLeftRadius: \"15px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4\",\n                                            style: {\n                                                paddingLeft: \"2rem\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"d-flex align-items-center mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-success bg-opacity-10 rounded-circle p-3 me-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBriefcase_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiUsers, {\n                                                                className: \"text-success\",\n                                                                size: 24\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"mb-0 fw-bold\",\n                                                            style: {\n                                                                fontSize: \"1.3rem\",\n                                                                color: \"#666\"\n                                                            },\n                                                            children: \"Benefits for freelance sales associates\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-unstyled\",\n                                                    style: {\n                                                        fontSize: \"0.9rem\",\n                                                        lineHeight: \"1.6\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Work wherever you want\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"—at home, in a caf\\xe9, or on the go—you choose when and where you work.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Earn attractive commissions:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \" Get a fair fee per deal closed. Your success is in your hands.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Diverse opportunities:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \" discover a wide range of projects and companies that match your skills and interests\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"15px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"6px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"15px\",\n                                                borderBottomLeftRadius: \"15px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4\",\n                                            style: {\n                                                paddingLeft: \"2rem\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"d-flex align-items-center mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-success bg-opacity-10 rounded-circle p-3 me-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBriefcase_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiBriefcase, {\n                                                                className: \"text-success\",\n                                                                size: 24\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"mb-0 fw-bold\",\n                                                            style: {\n                                                                fontSize: \"1.3rem\",\n                                                                color: \"#666\"\n                                                            },\n                                                            children: \"Benefits for companies\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"list-unstyled\",\n                                                    style: {\n                                                        fontSize: \"0.9rem\",\n                                                        lineHeight: \"1.6\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"More revenue, less risk\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"—only pay for results. No fixed salaries or recruitment costs – just a clear commission per deal closed.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Access to skilled sales reps:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 372,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \" Connect with motivated freelancers who are ready to grow your business.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"Quick and easy collaboration:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 376,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \" Post your brief, select the right sales agent, and let the deals flow.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 305,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row g-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm text-white\",\n                                    style: {\n                                        backgroundColor: \"#28a745\",\n                                        borderRadius: \"20px\",\n                                        minHeight: \"300px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card-body p-5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"fw-bold mb-4\",\n                                                style: {\n                                                    fontSize: \"1.8rem\"\n                                                },\n                                                children: \"Why should you choose us?\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mb-4\",\n                                                style: {\n                                                    fontSize: \"1rem\",\n                                                    lineHeight: \"1.6\"\n                                                },\n                                                children: \"To bridge this gap, we founded DealClosed. By linking companies with motivated freelance sales staff, we offer a win-win situation: companies achieve results without unnecessary overhead and sales employees get the flexibility to work how, when and where they want.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn px-4 py-2 rounded-pill\",\n                                                style: {\n                                                    backgroundColor: \"#1e7e34\",\n                                                    border: \"none\",\n                                                    color: \"white\",\n                                                    fontSize: \"0.9rem\",\n                                                    fontWeight: \"400\",\n                                                    transition: \"all 0.3s ease\"\n                                                },\n                                                onMouseEnter: (e)=>{\n                                                    const target = e.target;\n                                                    target.style.backgroundColor = \"white\";\n                                                    target.style.color = \"#28a745\";\n                                                },\n                                                onMouseLeave: (e)=>{\n                                                    const target = e.target;\n                                                    target.style.backgroundColor = \"#1e7e34\";\n                                                    target.style.color = \"white\";\n                                                },\n                                                onClick: ()=>window.location.href = \"/about\",\n                                                children: \"Learn more →\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"20px\",\n                                        minHeight: \"300px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"8px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"20px\",\n                                                borderBottomLeftRadius: \"20px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4 text-center d-flex flex-column justify-content-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center\",\n                                                        style: {\n                                                            width: \"60px\",\n                                                            height: \"60px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"30\",\n                                                            height: \"30\",\n                                                            fill: \"#28a745\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8 1a2.5 2.5 0 0 1 2.5 2.5V4h-5v-.5A2.5 2.5 0 0 1 8 1zm3.5 3v-.5a3.5 3.5 0 1 0-7 0V4H1v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4h-3.5zM2 5h12v9a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V5z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold mb-3\",\n                                                    style: {\n                                                        fontSize: \"1.2rem\"\n                                                    },\n                                                    children: \"Transparency\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted\",\n                                                    style: {\n                                                        fontSize: \"0.9rem\",\n                                                        lineHeight: \"1.5\"\n                                                    },\n                                                    children: \"With a simple 15% commission on successful deals, we guarantee fairness and clarity in every transaction.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"20px\",\n                                        minHeight: \"300px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"8px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"20px\",\n                                                borderBottomLeftRadius: \"20px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4 text-center d-flex flex-column justify-content-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center\",\n                                                        style: {\n                                                            width: \"60px\",\n                                                            height: \"60px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"30\",\n                                                            height: \"30\",\n                                                            fill: \"#28a745\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492zM5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52l-.094-.319z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                    lineNumber: 474,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold mb-3\",\n                                                    style: {\n                                                        fontSize: \"1.2rem\"\n                                                    },\n                                                    children: \"Flexibility\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted\",\n                                                    style: {\n                                                        fontSize: \"0.9rem\",\n                                                        lineHeight: \"1.5\"\n                                                    },\n                                                    children: \"We embrace the future of work and offer sellers and companies freedom and choice.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card h-100 border-0 shadow-sm position-relative\",\n                                    style: {\n                                        borderRadius: \"20px\",\n                                        minHeight: \"300px\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"position-absolute top-0 start-0 h-100\",\n                                            style: {\n                                                width: \"8px\",\n                                                backgroundColor: \"#28a745\",\n                                                borderTopLeftRadius: \"20px\",\n                                                borderBottomLeftRadius: \"20px\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body p-4 text-center d-flex flex-column justify-content-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center\",\n                                                        style: {\n                                                            width: \"60px\",\n                                                            height: \"60px\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            width: \"30\",\n                                                            height: \"30\",\n                                                            fill: \"#28a745\",\n                                                            viewBox: \"0 0 16 16\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8.5 4.5a.5.5 0 0 0-1 0v3h-3a.5.5 0 0 0 0 1h3v3a.5.5 0 0 0 1 0v-3h3a.5.5 0 0 0 0-1h-3v-3z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 503,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold mb-3\",\n                                                    style: {\n                                                        fontSize: \"1.2rem\"\n                                                    },\n                                                    children: \"Focus on results\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted\",\n                                                    style: {\n                                                        fontSize: \"0.9rem\",\n                                                        lineHeight: \"1.5\"\n                                                    },\n                                                    children: \"We believe in rewarding results, not effort. We create a performance-oriented culture for everyone involved.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 388,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 387,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-5 bg-light\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"fw-bold mb-4\",\n                            children: \"Assignments\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 523,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                            className: \"text-muted mb-4\",\n                            children: \"Latest Assignments\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 524,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card border-0 shadow-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-img-top bg-primary d-flex align-items-center justify-content-center\",\n                                            style: {\n                                                height: \"200px\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"60\",\n                                                        height: \"60\",\n                                                        fill: \"white\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854V1.146zM1.175 1a.146.146 0 0 0-.146.146v13.708c0 .08.066.146.146.146h13.65a.146.146 0 0 0 .146-.146V1.146a.146.146 0 0 0-.146-.146H1.175z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                                lineNumber: 533,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                                        className: \"mt-2 mb-0\",\n                                                        children: \"Information Technology\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"badge bg-primary mb-2\",\n                                                    children: \"Information Technology\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-success fs-4 fw-bold\",\n                                                    children: \"$\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"fw-bold mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/assignments/5\",\n                                                        className: \"text-decoration-none text-dark\",\n                                                        children: \"Web application development\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted mb-2\",\n                                                    children: \"Test Assignment\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Payment Type:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 39\n                                                        }, undefined),\n                                                        \" Hourly ($17.00)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mb-3\",\n                                                    children: \"Applications: 1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/login\",\n                                                    className: \"btn btn-success w-100\",\n                                                    children: \"Take This Deal\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 526,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 522,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 521,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white py-5 border-top\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-lg-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                            className: \"fw-bold text-success mb-4\",\n                                            children: \"DealClosed\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted mb-2\",\n                                            children: \"Amsterdam, The Netherlands\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Email:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 46\n                                                }, undefined),\n                                                \" <EMAIL>\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-lg-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                            className: \"fw-bold mb-3 text-success\",\n                                            children: \"Useful links\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"list-unstyled\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Home\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 572,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/about\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"About us\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/assignments\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Assignments\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-lg-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                            className: \"fw-bold mb-3 text-success\",\n                                            children: \"Other links\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"list-unstyled\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/contact\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Contact Us\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/terms\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Terms of service\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/privacy\",\n                                                        className: \"text-muted text-decoration-none\",\n                                                        children: \"Privacy policy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 38\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 583,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 578,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-lg-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                            className: \"fw-bold mb-3 text-success\",\n                                            children: \"Follow us\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 588,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"d-flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"btn btn-outline-secondary btn-sm rounded-circle\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M5.026 15c6.038 0 9.341-5.003 9.341-9.334 0-.14 0-.282-.006-.422A6.685 6.685 0 0 0 16 3.542a6.658 6.658 0 0 1-1.889.518 3.301 3.301 0 0 0 1.447-1.817 6.533 6.533 0 0 1-2.087.793A3.286 3.286 0 0 0 7.875 6.03a9.325 9.325 0 0 1-6.767-3.429 3.289 3.289 0 0 0 1.018 4.382A3.323 3.323 0 0 1 .64 6.575v.045a3.288 3.288 0 0 0 2.632 3.218 3.203 3.203 0 0 1-.865.115 3.23 3.23 0 0 1-.614-.057 3.283 3.283 0 0 0 3.067 2.277A6.588 6.588 0 0 1 .78 13.58a6.32 6.32 0 0 1-.78-.045A9.344 9.344 0 0 0 5.026 15z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 591,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 590,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"btn btn-outline-secondary btn-sm rounded-circle\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M16 8.049c0-4.446-3.582-8.05-8-8.05C3.58 0-.002 3.603-.002 8.05c0 4.017 2.926 7.347 6.75 7.951v-5.625h-2.03V8.05H6.75V6.275c0-2.017 1.195-3.131 3.022-3.131.876 0 1.791.157 1.791.157v1.98h-1.009c-.993 0-1.303.621-1.303 1.258v1.51h2.218l-.354 2.326H9.25V16c3.824-.604 6.75-3.934 6.75-7.951z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 596,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"btn btn-outline-secondary btn-sm rounded-circle\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M8 0C5.829 0 5.556.01 4.703.048 3.85.088 3.269.222 2.76.42a3.917 3.917 0 0 0-1.417.923A3.927 3.927 0 0 0 .42 2.76C.222 3.268.087 3.85.048 4.7.01 5.555 0 5.827 0 8.001c0 2.172.01 2.444.048 3.297.04.852.174 1.433.372 1.942.205.526.478.972.923 1.417.444.445.89.719 1.416.923.51.198 1.09.333 1.942.372C5.555 15.99 5.827 16 8 16s2.444-.01 3.298-.048c.851-.04 1.434-.174 1.943-.372a3.916 3.916 0 0 0 1.416-.923c.445-.445.718-.891.923-1.417.197-.509.332-1.09.372-1.942C15.99 10.445 16 10.173 16 8s-.01-2.445-.048-3.299c-.04-.851-.175-1.433-.372-1.941a3.926 3.926 0 0 0-.923-1.417A3.911 3.911 0 0 0 13.24.42c-.51-.198-1.092-.333-1.943-.372C10.443.01 10.172 0 7.998 0h.003zm-.717 1.442h.718c2.136 0 2.389.007 3.232.046.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.598.92.11.281.24.705.275 1.485.039.843.047 1.096.047 3.231s-.008 2.389-.047 3.232c-.035.78-.166 1.203-.275 1.485a2.47 2.47 0 0 1-.599.919c-.28.28-.546.453-.92.598-.28.11-.704.24-1.485.276-.843.038-1.096.047-3.232.047s-2.39-.009-3.233-.047c-.78-.036-1.203-.166-1.485-.276a2.478 2.478 0 0 1-.92-.598 2.48 2.48 0 0 1-.6-.92c-.109-.281-.24-.705-.275-1.485-.038-.843-.046-1.096-.046-3.233 0-2.136.008-2.388.046-3.231.036-.78.166-1.204.276-1.486.145-.373.319-.64.599-.92.28-.28.546-.453.92-.598.282-.11.705-.24 1.485-.276.738-.034 1.024-.044 2.515-.045v.002zm4.988 1.328a.96.96 0 1 0 0 1.92.96.96 0 0 0 0-1.92zm-4.27 1.122a4.109 4.109 0 1 0 0 8.217 4.109 4.109 0 0 0 0-8.217zm0 1.441a2.667 2.667 0 1 1 0 5.334 2.667 2.667 0 0 1 0-5.334z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 601,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"btn btn-outline-secondary btn-sm rounded-circle\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 16 16\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M0 1.146C0 .513.526 0 1.175 0h13.65C15.474 0 16 .513 16 1.146v13.708c0 .633-.526 1.146-1.175 1.146H1.175C.526 16 0 15.487 0 14.854V1.146zm4.943 12.248V6.169H2.542v7.225h2.401zm-1.2-8.212c.837 0 1.358-.554 1.358-1.248-.015-.709-.52-1.248-1.342-1.248-.822 0-1.359.54-1.359 1.248 0 .694.521 1.248 1.327 1.248h.016zm4.908 8.212V9.359c0-.216.016-.432.08-.586.173-.431.568-.878 1.232-.878.869 0 1.216.662 1.216 1.634v3.865h2.401V9.25c0-2.22-1.184-3.252-2.764-3.252-1.274 0-1.845.7-2.165 1.193v.025h-.016a5.54 5.54 0 0 1 .016-.025V6.169h-2.4c.03.678 0 7.225 0 7.225h2.4z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                            lineNumber: 607,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                        lineNumber: 606,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 589,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 562,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                            className: \"my-4\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 614,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"row align-items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-md-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted mb-0\",\n                                        children: [\n                                            \"\\xa9 Copyright \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"DealClosed,\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                                lineNumber: 618,\n                                                columnNumber: 58\n                                            }, undefined),\n                                            \" All Rights Reserved\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-md-6 text-end\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/us.png\",\n                                            alt: \"English\",\n                                            width: \"20\",\n                                            className: \"me-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/nl.png\",\n                                            alt: \"Dutch\",\n                                            width: \"20\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                            lineNumber: 622,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                                    lineNumber: 620,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                            lineNumber: 616,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                    lineNumber: 561,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\FAST\\\\Freelance\\\\Project9_SalesPlatform\\\\DealClosed\\\\frontend\\\\src\\\\pages\\\\index.tsx\",\n                lineNumber: 560,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_c = HomePage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HomePage);\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvaW5kZXgudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTZCO0FBQ0E7QUFDSDtBQUM0QjtBQUV0RCxNQUFNSyxXQUFxQjtJQUN6QixxQkFDRTs7MEJBQ0UsOERBQUNMLGtEQUFJQTs7a0NBQ0gsOERBQUNNO2tDQUFNOzs7Ozs7a0NBQ1AsOERBQUNDO3dCQUFLQyxNQUFLO3dCQUFjQyxTQUFROzs7Ozs7a0NBQ2pDLDhEQUFDRjt3QkFBS0MsTUFBSzt3QkFBV0MsU0FBUTs7Ozs7O2tDQUM5Qiw4REFBQ0M7d0JBQUtDLEtBQUk7d0JBQU9DLE1BQUs7Ozs7Ozs7Ozs7OzswQkFJeEIsOERBQUNDO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDQztvQkFBSUQsV0FBVTs7c0NBQ2IsOERBQUNiLGtEQUFJQTs0QkFBQ1csTUFBSzs0QkFBSUUsV0FBVTs7OENBQ3ZCLDhEQUFDQztvQ0FBSUQsV0FBVTs4Q0FDYiw0RUFBQ0U7d0NBQUlDLE9BQU07d0NBQUtDLFFBQU87d0NBQUtDLFNBQVE7d0NBQWNDLE1BQUs7OzBEQUNyRCw4REFBQ0M7Z0RBQU9DLElBQUc7Z0RBQUtDLElBQUc7Z0RBQUtDLEdBQUU7Z0RBQUtKLE1BQUs7Ozs7OzswREFDcEMsOERBQUNLO2dEQUFLQyxHQUFFO2dEQUFxQkMsUUFBTztnREFBUUMsYUFBWTtnREFBSUMsZUFBYztnREFBUUMsZ0JBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUdyRyw4REFBQ0M7b0NBQUtqQixXQUFVO29DQUF1QmtCLE9BQU87d0NBQUVDLFVBQVU7b0NBQVM7OENBQUc7Ozs7Ozs7Ozs7OztzQ0FHeEUsOERBQUNDOzRCQUNDcEIsV0FBVTs0QkFDVnFCLE1BQUs7NEJBQ0xDLGtCQUFlOzRCQUNmQyxrQkFBZTtzQ0FFZiw0RUFBQ047Z0NBQUtqQixXQUFVOzs7Ozs7Ozs7OztzQ0FHbEIsOERBQUNDOzRCQUFJRCxXQUFVOzRCQUEyQndCLElBQUc7OzhDQUMzQyw4REFBQ0M7b0NBQUd6QixXQUFVOztzREFDWiw4REFBQzBCOzRDQUFHMUIsV0FBVTtzREFDWiw0RUFBQ2Isa0RBQUlBO2dEQUFDVyxNQUFLO2dEQUFJRSxXQUFVO2dEQUF3QmtCLE9BQU87b0RBQUVTLFlBQVk7b0RBQU9SLFVBQVU7Z0RBQVU7MERBQUc7Ozs7Ozs7Ozs7O3NEQUV0Ryw4REFBQ087NENBQUcxQixXQUFVO3NEQUNaLDRFQUFDYixrREFBSUE7Z0RBQUNXLE1BQUs7Z0RBQWVFLFdBQVU7Z0RBQXFCa0IsT0FBTztvREFBRVMsWUFBWTtvREFBT1IsVUFBVTtnREFBVTswREFBRzs7Ozs7Ozs7Ozs7c0RBRTlHLDhEQUFDTzs0Q0FBRzFCLFdBQVU7c0RBQ1osNEVBQUNiLGtEQUFJQTtnREFBQ1csTUFBSztnREFBZUUsV0FBVTtnREFBcUJrQixPQUFPO29EQUFFUyxZQUFZO29EQUFPUixVQUFVO2dEQUFVOzBEQUFHOzs7Ozs7Ozs7OztzREFFOUcsOERBQUNPOzRDQUFHMUIsV0FBVTtzREFDWiw0RUFBQ2Isa0RBQUlBO2dEQUFDVyxNQUFLO2dEQUFhRSxXQUFVO2dEQUFxQmtCLE9BQU87b0RBQUVTLFlBQVk7b0RBQU9SLFVBQVU7Z0RBQVU7MERBQUc7Ozs7Ozs7Ozs7O3NEQUU1Ryw4REFBQ087NENBQUcxQixXQUFVO3NEQUNaLDRFQUFDYixrREFBSUE7Z0RBQUNXLE1BQUs7Z0RBQVdFLFdBQVU7Z0RBQXFCa0IsT0FBTztvREFBRVMsWUFBWTtvREFBT1IsVUFBVTtnREFBVTswREFBRzs7Ozs7Ozs7Ozs7c0RBRTFHLDhEQUFDTzs0Q0FBRzFCLFdBQVU7c0RBQ1osNEVBQUNiLGtEQUFJQTtnREFBQ1csTUFBSztnREFBV0UsV0FBVTtnREFBcUJrQixPQUFPO29EQUFFUyxZQUFZO29EQUFPUixVQUFVO2dEQUFVOzBEQUFHOzs7Ozs7Ozs7OztzREFFMUcsOERBQUNPOzRDQUFHMUIsV0FBVTtzREFDWiw0RUFBQ2Isa0RBQUlBO2dEQUFDVyxNQUFLO2dEQUFRRSxXQUFVO2dEQUFxQmtCLE9BQU87b0RBQUVTLFlBQVk7b0RBQU9SLFVBQVU7Z0RBQVU7MERBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUl6Ryw4REFBQ2xCO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ2Isa0RBQUlBOzRDQUFDVyxNQUFLOzRDQUFTRSxXQUFVOzRDQUFxQmtCLE9BQU87Z0RBQUVTLFlBQVk7Z0RBQU9SLFVBQVU7NENBQVU7c0RBQUc7Ozs7OztzREFHdEcsOERBQUNoQyxrREFBSUE7NENBQUNXLE1BQUs7NENBQVlFLFdBQVU7NENBQXlDa0IsT0FBTztnREFBRVMsWUFBWTtnREFBT1IsVUFBVTs0Q0FBTztzREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUWxJLDhEQUFDUztnQkFBUTVCLFdBQVU7Z0JBQW9Ca0IsT0FBTztvQkFDNUNXLGlCQUFpQjtvQkFDakJDLGdCQUFnQjtvQkFDaEJDLG9CQUFvQjtvQkFDcEJDLFdBQVc7b0JBQ1hDLFdBQVc7Z0JBQ2I7MEJBQ0UsNEVBQUNoQztvQkFBSUQsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7a0NBQ2IsNEVBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ2tDO29DQUFHbEMsV0FBVTtvQ0FBeUJrQixPQUFPO3dDQUFFQyxVQUFVO3dDQUFVZ0IsWUFBWTtvQ0FBTTs7d0NBQUc7c0RBQ2hFLDhEQUFDQzs7Ozs7c0RBQ3hCLDhEQUFDbkI7NENBQUtDLE9BQU87Z0RBQUVDLFVBQVU7NENBQVM7c0RBQUc7Ozs7Ozs7Ozs7Ozs4Q0FFdkMsOERBQUNrQjtvQ0FBRXJDLFdBQVU7b0NBQVlrQixPQUFPO3dDQUFFQyxVQUFVO3dDQUFXbUIsU0FBUzt3Q0FBUUMsVUFBVTtvQ0FBUTs4Q0FBRzs7Ozs7OzhDQUk3Riw4REFBQ25CO29DQUNDcEIsV0FBVTtvQ0FDVmtCLE9BQU87d0NBQ0xzQixpQkFBaUI7d0NBQ2pCQyxRQUFRO3dDQUNSQyxPQUFPO3dDQUNQdkIsVUFBVTt3Q0FDVlEsWUFBWTt3Q0FDWmdCLFlBQVk7b0NBQ2Q7b0NBQ0FDLGNBQWMsQ0FBQ0M7d0NBQ2IsTUFBTUMsU0FBU0QsRUFBRUMsTUFBTTt3Q0FDdkJBLE9BQU81QixLQUFLLENBQUNzQixlQUFlLEdBQUc7d0NBQy9CTSxPQUFPNUIsS0FBSyxDQUFDNkIsV0FBVyxHQUFHO29DQUM3QjtvQ0FDQUMsY0FBYyxDQUFDSDt3Q0FDYixNQUFNQyxTQUFTRCxFQUFFQyxNQUFNO3dDQUN2QkEsT0FBTzVCLEtBQUssQ0FBQ3NCLGVBQWUsR0FBRzt3Q0FDL0JNLE9BQU81QixLQUFLLENBQUM2QixXQUFXLEdBQUc7b0NBQzdCO29DQUNBRSxTQUFTLElBQU1DLE9BQU9DLFFBQVEsQ0FBQ3JELElBQUksR0FBRzs4Q0FDdkM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTVCw4REFBQzhCO2dCQUFRNUIsV0FBVTtnQkFBT2tCLE9BQU87b0JBQUVzQixpQkFBaUI7Z0JBQVU7MEJBQzVELDRFQUFDdkM7b0JBQUlELFdBQVU7OEJBQ2IsNEVBQUNvRDt3QkFBR3BELFdBQVU7d0JBQWtCa0IsT0FBTzs0QkFBRUMsVUFBVTt3QkFBUztrQ0FBRzs7Ozs7Ozs7Ozs7Ozs7OzswQkFLbkUsOERBQUNTO2dCQUFRNUIsV0FBVTswQkFDakIsNEVBQUNDO29CQUFJRCxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTtrQ0FDYiw0RUFBQ0M7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDcUQ7b0NBQUdyRCxXQUFVO29DQUFla0IsT0FBTzt3Q0FBRUMsVUFBVTt3Q0FBVXVCLE9BQU87b0NBQU87OENBQUc7Ozs7Ozs4Q0FHM0UsOERBQUNMO29DQUFFckMsV0FBVTtvQ0FBT2tCLE9BQU87d0NBQUVDLFVBQVU7d0NBQVVnQixZQUFZO29DQUFNOztzREFDakUsOERBQUNtQjtzREFBTzs7Ozs7O3dDQUFtQjs7Ozs7Ozs4Q0FHN0IsOERBQUNqQjtvQ0FBRXJDLFdBQVU7b0NBQU9rQixPQUFPO3dDQUFFQyxVQUFVO3dDQUFVZ0IsWUFBWTtvQ0FBTTs4Q0FBRzs7Ozs7OzhDQUl0RSw4REFBQ0U7b0NBQUVuQixPQUFPO3dDQUFFQyxVQUFVO3dDQUFVZ0IsWUFBWTt3Q0FBT08sT0FBTzt3Q0FBUWEsV0FBVztvQ0FBUzs4Q0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVNqRyw4REFBQzNCO2dCQUFRNUIsV0FBVTswQkFDakIsNEVBQUNDO29CQUFJRCxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNDO2dDQUFJRCxXQUFVOzBDQUNiLDRFQUFDQztvQ0FBSUQsV0FBVTtvQ0FBa0RrQixPQUFPO3dDQUFFc0MsY0FBYzt3Q0FBUXhCLFdBQVc7b0NBQVE7O3NEQUNqSCw4REFBQy9COzRDQUNDRCxXQUFVOzRDQUNWa0IsT0FBTztnREFDTGYsT0FBTztnREFDUHFDLGlCQUFpQjtnREFDakJpQixxQkFBcUI7Z0RBQ3JCQyx3QkFBd0I7NENBQzFCOzs7Ozs7c0RBRUYsOERBQUN6RDs0Q0FBSUQsV0FBVTs0Q0FBZ0JrQixPQUFPO2dEQUFFeUMsYUFBYTs0Q0FBTzs7OERBQzFELDhEQUFDMUQ7b0RBQUlELFdBQVU7OERBQ2IsNEVBQUNDO3dEQUFJRCxXQUFVO3dEQUFrR2tCLE9BQU87NERBQUVmLE9BQU87NERBQVFDLFFBQVE7d0RBQU87a0VBQ3RKLDRFQUFDRjs0REFBSUMsT0FBTTs0REFBS0MsUUFBTzs0REFBS0UsTUFBSzs0REFBVUQsU0FBUTs7OEVBQ2pELDhEQUFDTTtvRUFBS0MsR0FBRTs7Ozs7OzhFQUNSLDhEQUFDRDtvRUFBS2lELFVBQVM7b0VBQVVoRCxHQUFFOzs7Ozs7OEVBQzNCLDhEQUFDRDtvRUFBS0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFJZCw4REFBQ2lEO29EQUFHN0QsV0FBVTtvREFBMkJrQixPQUFPO3dEQUFFQyxVQUFVO3dEQUFVdUIsT0FBTztvREFBTzs4REFBRzs7Ozs7OzhEQUN2Riw4REFBQ2pCO29EQUFHekIsV0FBVTtvREFBZ0JrQixPQUFPO3dEQUFFQyxVQUFVO3dEQUFRZ0IsWUFBWTtvREFBTTs7c0VBQ3pFLDhEQUFDVDs0REFBRzFCLFdBQVU7O2dFQUFPOzhFQUNqQiw4REFBQ3NEOzhFQUFPOzs7Ozs7Z0VBQStCOzs7Ozs7O3NFQUczQyw4REFBQzVCOzREQUFHMUIsV0FBVTs7Z0VBQU87OEVBQ2pCLDhEQUFDc0Q7OEVBQU87Ozs7OztnRUFBcUM7Ozs7Ozs7c0VBR2pELDhEQUFDNUI7NERBQUcxQixXQUFVOztnRUFBTzs4RUFDakIsOERBQUNzRDs4RUFBTzs7Ozs7O2dFQUErQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU9uRCw4REFBQ3JEO2dDQUFJRCxXQUFVOzBDQUNiLDRFQUFDQztvQ0FBSUQsV0FBVTtvQ0FBa0RrQixPQUFPO3dDQUFFc0MsY0FBYzt3Q0FBUXhCLFdBQVc7b0NBQVE7O3NEQUNqSCw4REFBQy9COzRDQUNDRCxXQUFVOzRDQUNWa0IsT0FBTztnREFDTGYsT0FBTztnREFDUHFDLGlCQUFpQjtnREFDakJpQixxQkFBcUI7Z0RBQ3JCQyx3QkFBd0I7NENBQzFCOzs7Ozs7c0RBRUYsOERBQUN6RDs0Q0FBSUQsV0FBVTs0Q0FBZ0JrQixPQUFPO2dEQUFFeUMsYUFBYTs0Q0FBTzs7OERBQzFELDhEQUFDMUQ7b0RBQUlELFdBQVU7OERBQ2IsNEVBQUNDO3dEQUFJRCxXQUFVO3dEQUFrR2tCLE9BQU87NERBQUVmLE9BQU87NERBQVFDLFFBQVE7d0RBQU87a0VBQ3RKLDRFQUFDRjs0REFBSUMsT0FBTTs0REFBS0MsUUFBTzs0REFBS0UsTUFBSzs0REFBVUQsU0FBUTs7OEVBQ2pELDhEQUFDTTtvRUFBS0MsR0FBRTs7Ozs7OzhFQUNSLDhEQUFDRDtvRUFBS0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFJZCw4REFBQ2lEO29EQUFHN0QsV0FBVTtvREFBMkJrQixPQUFPO3dEQUFFQyxVQUFVO3dEQUFVdUIsT0FBTztvREFBTzs4REFBRzs7Ozs7OzhEQUN2Riw4REFBQ2pCO29EQUFHekIsV0FBVTtvREFBZ0JrQixPQUFPO3dEQUFFQyxVQUFVO3dEQUFRZ0IsWUFBWTtvREFBTTs7c0VBQ3pFLDhEQUFDVDs0REFBRzFCLFdBQVU7O2dFQUFPOzhFQUNqQiw4REFBQ3NEOzhFQUFPOzs7Ozs7Z0VBQWdDOzs7Ozs7O3NFQUc1Qyw4REFBQzVCOzREQUFHMUIsV0FBVTs7Z0VBQU87OEVBQ2pCLDhEQUFDc0Q7OEVBQU87Ozs7OztnRUFBc0M7Ozs7Ozs7c0VBR2xELDhEQUFDNUI7NERBQUcxQixXQUFVOztnRUFBTzs4RUFDakIsOERBQUNzRDs4RUFBTzs7Ozs7O2dFQUFzQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFZaEUsOERBQUMxQjtnQkFBUTVCLFdBQVU7MEJBQ2pCLDRFQUFDQztvQkFBSUQsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDQztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNxRDt3Q0FBR3JELFdBQVU7d0NBQWVrQixPQUFPOzRDQUFFQyxVQUFVOzRDQUFVdUIsT0FBTzt3Q0FBTztrREFBRzs7Ozs7O2tEQUMzRSw4REFBQ0w7d0NBQUVyQyxXQUFVO3dDQUFrQmtCLE9BQU87NENBQUVDLFVBQVU7NENBQVFnQixZQUFZOzRDQUFPb0IsV0FBVzt3Q0FBUztrREFBRzs7Ozs7O2tEQUtwRyw4REFBQ3REO3dDQUFJRCxXQUFVO2tEQUNiLDRFQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNDO29EQUFJRCxXQUFVOzhEQUNiLDRFQUFDaUI7d0RBQUtqQixXQUFVO3dEQUFla0IsT0FBTzs0REFBRUMsVUFBVTt3REFBUztrRUFBRzs7Ozs7Ozs7Ozs7OERBRWhFLDhEQUFDbEI7O3NFQUNDLDhEQUFDNkQ7NERBQUc5RCxXQUFVOzREQUFPa0IsT0FBTztnRUFBRUMsVUFBVTtnRUFBVVEsWUFBWTs0REFBTTtzRUFBRzs7Ozs7O3NFQUN2RSw4REFBQ1U7NERBQUVuQixPQUFPO2dFQUFFQyxVQUFVO2dFQUFXZ0IsWUFBWTtnRUFBTzRCLFFBQVE7NERBQUk7c0VBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQVF6RSw4REFBQzlEO3dDQUFJRCxXQUFVO2tEQUNiLDRFQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNDO29EQUFJRCxXQUFVOzhEQUNiLDRFQUFDaUI7d0RBQUtqQixXQUFVO3dEQUFla0IsT0FBTzs0REFBRUMsVUFBVTt3REFBUztrRUFBRzs7Ozs7Ozs7Ozs7OERBRWhFLDhEQUFDbEI7O3NFQUNDLDhEQUFDNkQ7NERBQUc5RCxXQUFVOzREQUFPa0IsT0FBTztnRUFBRUMsVUFBVTtnRUFBVVEsWUFBWTs0REFBTTtzRUFBRzs7Ozs7O3NFQUN2RSw4REFBQ1U7NERBQUVuQixPQUFPO2dFQUFFQyxVQUFVO2dFQUFXZ0IsWUFBWTtnRUFBTzRCLFFBQVE7NERBQUk7c0VBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQVN6RSw4REFBQzNDO3dDQUNDcEIsV0FBVTt3Q0FDVmtCLE9BQU87NENBQUVDLFVBQVU7NENBQVVRLFlBQVk7d0NBQU07d0NBQy9Dc0IsU0FBUyxJQUFNQyxPQUFPQyxRQUFRLENBQUNyRCxJQUFJLEdBQUc7a0RBQ3ZDOzs7Ozs7Ozs7Ozs7MENBSUgsOERBQUNHO2dDQUFJRCxXQUFVOzBDQUNiLDRFQUFDQztvQ0FDQ0QsV0FBVTtvQ0FDVmtCLE9BQU87d0NBQ0xkLFFBQVE7d0NBQ1J5QixpQkFBaUI7d0NBQ2pCQyxnQkFBZ0I7d0NBQ2hCQyxvQkFBb0I7d0NBQ3BCaUMsa0JBQWtCO29DQUNwQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVFWLDhEQUFDcEM7Z0JBQVE1QixXQUFVOzBCQUNqQiw0RUFBQ0M7b0JBQUlELFdBQVU7OEJBQ2IsNEVBQUNDO3dCQUFJRCxXQUFVOzswQ0FFYiw4REFBQ0M7Z0NBQUlELFdBQVU7MENBQ2IsNEVBQUNDO29DQUFJRCxXQUFVO29DQUFrRGtCLE9BQU87d0NBQUVzQyxjQUFjO29DQUFPOztzREFDN0YsOERBQUN2RDs0Q0FDQ0QsV0FBVTs0Q0FDVmtCLE9BQU87Z0RBQ0xmLE9BQU87Z0RBQ1BxQyxpQkFBaUI7Z0RBQ2pCaUIscUJBQXFCO2dEQUNyQkMsd0JBQXdCOzRDQUMxQjs7Ozs7O3NEQUVGLDhEQUFDekQ7NENBQUlELFdBQVU7NENBQWdCa0IsT0FBTztnREFBRXlDLGFBQWE7NENBQU87OzhEQUMxRCw4REFBQzFEO29EQUFJRCxXQUFVOztzRUFDYiw4REFBQ0M7NERBQUlELFdBQVU7c0VBQ2IsNEVBQUNWLDhGQUFPQTtnRUFBQ1UsV0FBVTtnRUFBZWlFLE1BQU07Ozs7Ozs7Ozs7O3NFQUUxQyw4REFBQ0M7NERBQUdsRSxXQUFVOzREQUFla0IsT0FBTztnRUFBRUMsVUFBVTtnRUFBVXVCLE9BQU87NERBQU87c0VBQUc7Ozs7Ozs7Ozs7Ozs4REFHN0UsOERBQUNqQjtvREFBR3pCLFdBQVU7b0RBQWdCa0IsT0FBTzt3REFBRUMsVUFBVTt3REFBVWdCLFlBQVk7b0RBQU07O3NFQUMzRSw4REFBQ1Q7NERBQUcxQixXQUFVOzs4RUFDWiw4REFBQ3NEOzhFQUFPOzs7Ozs7Z0VBQStCOzs7Ozs7O3NFQUd6Qyw4REFBQzVCOzREQUFHMUIsV0FBVTs7OEVBQ1osOERBQUNzRDs4RUFBTzs7Ozs7O2dFQUFxQzs7Ozs7OztzRUFHL0MsOERBQUM1Qjs0REFBRzFCLFdBQVU7OzhFQUNaLDhEQUFDc0Q7OEVBQU87Ozs7OztnRUFBK0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FTakQsOERBQUNyRDtnQ0FBSUQsV0FBVTswQ0FDYiw0RUFBQ0M7b0NBQUlELFdBQVU7b0NBQWtEa0IsT0FBTzt3Q0FBRXNDLGNBQWM7b0NBQU87O3NEQUM3Riw4REFBQ3ZEOzRDQUNDRCxXQUFVOzRDQUNWa0IsT0FBTztnREFDTGYsT0FBTztnREFDUHFDLGlCQUFpQjtnREFDakJpQixxQkFBcUI7Z0RBQ3JCQyx3QkFBd0I7NENBQzFCOzs7Ozs7c0RBRUYsOERBQUN6RDs0Q0FBSUQsV0FBVTs0Q0FBZ0JrQixPQUFPO2dEQUFFeUMsYUFBYTs0Q0FBTzs7OERBQzFELDhEQUFDMUQ7b0RBQUlELFdBQVU7O3NFQUNiLDhEQUFDQzs0REFBSUQsV0FBVTtzRUFDYiw0RUFBQ1gsa0dBQVdBO2dFQUFDVyxXQUFVO2dFQUFlaUUsTUFBTTs7Ozs7Ozs7Ozs7c0VBRTlDLDhEQUFDQzs0REFBR2xFLFdBQVU7NERBQWVrQixPQUFPO2dFQUFFQyxVQUFVO2dFQUFVdUIsT0FBTzs0REFBTztzRUFBRzs7Ozs7Ozs7Ozs7OzhEQUc3RSw4REFBQ2pCO29EQUFHekIsV0FBVTtvREFBZ0JrQixPQUFPO3dEQUFFQyxVQUFVO3dEQUFVZ0IsWUFBWTtvREFBTTs7c0VBQzNFLDhEQUFDVDs0REFBRzFCLFdBQVU7OzhFQUNaLDhEQUFDc0Q7OEVBQU87Ozs7OztnRUFBZ0M7Ozs7Ozs7c0VBRzFDLDhEQUFDNUI7NERBQUcxQixXQUFVOzs4RUFDWiw4REFBQ3NEOzhFQUFPOzs7Ozs7Z0VBQXNDOzs7Ozs7O3NFQUdoRCw4REFBQzVCOzREQUFHMUIsV0FBVTs7OEVBQ1osOERBQUNzRDs4RUFBTzs7Ozs7O2dFQUFzQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFXOUQsOERBQUMxQjtnQkFBUTVCLFdBQVU7MEJBQ2pCLDRFQUFDQztvQkFBSUQsV0FBVTs4QkFDYiw0RUFBQ0M7d0JBQUlELFdBQVU7OzBDQUViLDhEQUFDQztnQ0FBSUQsV0FBVTswQ0FDYiw0RUFBQ0M7b0NBQUlELFdBQVU7b0NBQTJDa0IsT0FBTzt3Q0FBRXNCLGlCQUFpQjt3Q0FBV2dCLGNBQWM7d0NBQVF4QixXQUFXO29DQUFROzhDQUN0SSw0RUFBQy9CO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ2tFO2dEQUFHbEUsV0FBVTtnREFBZWtCLE9BQU87b0RBQUVDLFVBQVU7Z0RBQVM7MERBQUc7Ozs7OzswREFDNUQsOERBQUNrQjtnREFBRXJDLFdBQVU7Z0RBQU9rQixPQUFPO29EQUFFQyxVQUFVO29EQUFRZ0IsWUFBWTtnREFBTTswREFBRzs7Ozs7OzBEQUtwRSw4REFBQ2Y7Z0RBQ0NwQixXQUFVO2dEQUNWa0IsT0FBTztvREFDTHNCLGlCQUFpQjtvREFDakJDLFFBQVE7b0RBQ1JDLE9BQU87b0RBQ1B2QixVQUFVO29EQUNWUSxZQUFZO29EQUNaZ0IsWUFBWTtnREFDZDtnREFDQUMsY0FBYyxDQUFDQztvREFDYixNQUFNQyxTQUFTRCxFQUFFQyxNQUFNO29EQUN2QkEsT0FBTzVCLEtBQUssQ0FBQ3NCLGVBQWUsR0FBRztvREFDL0JNLE9BQU81QixLQUFLLENBQUN3QixLQUFLLEdBQUc7Z0RBQ3ZCO2dEQUNBTSxjQUFjLENBQUNIO29EQUNiLE1BQU1DLFNBQVNELEVBQUVDLE1BQU07b0RBQ3ZCQSxPQUFPNUIsS0FBSyxDQUFDc0IsZUFBZSxHQUFHO29EQUMvQk0sT0FBTzVCLEtBQUssQ0FBQ3dCLEtBQUssR0FBRztnREFDdkI7Z0RBQ0FPLFNBQVMsSUFBTUMsT0FBT0MsUUFBUSxDQUFDckQsSUFBSSxHQUFHOzBEQUN2Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FRUCw4REFBQ0c7Z0NBQUlELFdBQVU7MENBQ2IsNEVBQUNDO29DQUFJRCxXQUFVO29DQUFrRGtCLE9BQU87d0NBQUVzQyxjQUFjO3dDQUFReEIsV0FBVztvQ0FBUTs7c0RBQ2pILDhEQUFDL0I7NENBQ0NELFdBQVU7NENBQ1ZrQixPQUFPO2dEQUNMZixPQUFPO2dEQUNQcUMsaUJBQWlCO2dEQUNqQmlCLHFCQUFxQjtnREFDckJDLHdCQUF3Qjs0Q0FDMUI7Ozs7OztzREFFRiw4REFBQ3pEOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ0M7b0RBQUlELFdBQVU7OERBQ2IsNEVBQUNDO3dEQUFJRCxXQUFVO3dEQUFrR2tCLE9BQU87NERBQUVmLE9BQU87NERBQVFDLFFBQVE7d0RBQU87a0VBQ3RKLDRFQUFDRjs0REFBSUMsT0FBTTs0REFBS0MsUUFBTzs0REFBS0UsTUFBSzs0REFBVUQsU0FBUTtzRUFDakQsNEVBQUNNO2dFQUFLQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBSWQsOERBQUNrRDtvREFBRzlELFdBQVU7b0RBQWVrQixPQUFPO3dEQUFFQyxVQUFVO29EQUFTOzhEQUFHOzs7Ozs7OERBQzVELDhEQUFDa0I7b0RBQUVyQyxXQUFVO29EQUFha0IsT0FBTzt3REFBRUMsVUFBVTt3REFBVWdCLFlBQVk7b0RBQU07OERBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQVNsRiw4REFBQ2xDO2dDQUFJRCxXQUFVOzBDQUNiLDRFQUFDQztvQ0FBSUQsV0FBVTtvQ0FBa0RrQixPQUFPO3dDQUFFc0MsY0FBYzt3Q0FBUXhCLFdBQVc7b0NBQVE7O3NEQUNqSCw4REFBQy9COzRDQUNDRCxXQUFVOzRDQUNWa0IsT0FBTztnREFDTGYsT0FBTztnREFDUHFDLGlCQUFpQjtnREFDakJpQixxQkFBcUI7Z0RBQ3JCQyx3QkFBd0I7NENBQzFCOzs7Ozs7c0RBRUYsOERBQUN6RDs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNDO29EQUFJRCxXQUFVOzhEQUNiLDRFQUFDQzt3REFBSUQsV0FBVTt3REFBa0drQixPQUFPOzREQUFFZixPQUFPOzREQUFRQyxRQUFRO3dEQUFPO2tFQUN0Siw0RUFBQ0Y7NERBQUlDLE9BQU07NERBQUtDLFFBQU87NERBQUtFLE1BQUs7NERBQVVELFNBQVE7OzhFQUNqRCw4REFBQ007b0VBQUtDLEdBQUU7Ozs7Ozs4RUFDUiw4REFBQ0Q7b0VBQUtDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBSWQsOERBQUNrRDtvREFBRzlELFdBQVU7b0RBQWVrQixPQUFPO3dEQUFFQyxVQUFVO29EQUFTOzhEQUFHOzs7Ozs7OERBQzVELDhEQUFDa0I7b0RBQUVyQyxXQUFVO29EQUFha0IsT0FBTzt3REFBRUMsVUFBVTt3REFBVWdCLFlBQVk7b0RBQU07OERBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQVNsRiw4REFBQ2xDO2dDQUFJRCxXQUFVOzBDQUNiLDRFQUFDQztvQ0FBSUQsV0FBVTtvQ0FBa0RrQixPQUFPO3dDQUFFc0MsY0FBYzt3Q0FBUXhCLFdBQVc7b0NBQVE7O3NEQUNqSCw4REFBQy9COzRDQUNDRCxXQUFVOzRDQUNWa0IsT0FBTztnREFDTGYsT0FBTztnREFDUHFDLGlCQUFpQjtnREFDakJpQixxQkFBcUI7Z0RBQ3JCQyx3QkFBd0I7NENBQzFCOzs7Ozs7c0RBRUYsOERBQUN6RDs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNDO29EQUFJRCxXQUFVOzhEQUNiLDRFQUFDQzt3REFBSUQsV0FBVTt3REFBa0drQixPQUFPOzREQUFFZixPQUFPOzREQUFRQyxRQUFRO3dEQUFPO2tFQUN0Siw0RUFBQ0Y7NERBQUlDLE9BQU07NERBQUtDLFFBQU87NERBQUtFLE1BQUs7NERBQVVELFNBQVE7c0VBQ2pELDRFQUFDTTtnRUFBS0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUlkLDhEQUFDa0Q7b0RBQUc5RCxXQUFVO29EQUFla0IsT0FBTzt3REFBRUMsVUFBVTtvREFBUzs4REFBRzs7Ozs7OzhEQUM1RCw4REFBQ2tCO29EQUFFckMsV0FBVTtvREFBYWtCLE9BQU87d0RBQUVDLFVBQVU7d0RBQVVnQixZQUFZO29EQUFNOzhEQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBYXhGLDhEQUFDUDtnQkFBUTVCLFdBQVU7MEJBQ2pCLDRFQUFDQztvQkFBSUQsV0FBVTs7c0NBQ2IsOERBQUNxRDs0QkFBR3JELFdBQVU7c0NBQWU7Ozs7OztzQ0FDN0IsOERBQUM4RDs0QkFBRzlELFdBQVU7c0NBQWtCOzs7Ozs7c0NBRWhDLDhEQUFDQzs0QkFBSUQsV0FBVTtzQ0FDYiw0RUFBQ0M7Z0NBQUlELFdBQVU7MENBQ2IsNEVBQUNDO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ0M7NENBQUlELFdBQVU7NENBQTJFa0IsT0FBTztnREFBRWQsUUFBUTs0Q0FBUTtzREFDakgsNEVBQUNIO2dEQUFJRCxXQUFVOztrRUFDYiw4REFBQ0U7d0RBQUlDLE9BQU07d0RBQUtDLFFBQU87d0RBQUtFLE1BQUs7d0RBQVFELFNBQVE7OzBFQUMvQyw4REFBQ007Z0VBQUtDLEdBQUU7Ozs7OzswRUFDUiw4REFBQ0Q7Z0VBQUtDLEdBQUU7Ozs7Ozs7Ozs7OztrRUFFViw4REFBQ3dDO3dEQUFHcEQsV0FBVTtrRUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBRzlCLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNpQjtvREFBS2pCLFdBQVU7OERBQXdCOzs7Ozs7OERBQ3hDLDhEQUFDaUI7b0RBQUtqQixXQUFVOzhEQUE0Qjs7Ozs7OzhEQUM1Qyw4REFBQzhEO29EQUFHOUQsV0FBVTs4REFDWiw0RUFBQ2Isa0RBQUlBO3dEQUFDVyxNQUFLO3dEQUFpQkUsV0FBVTtrRUFBaUM7Ozs7Ozs7Ozs7OzhEQUl6RSw4REFBQ3FDO29EQUFFckMsV0FBVTs4REFBa0I7Ozs7Ozs4REFDL0IsOERBQUNxQztvREFBRXJDLFdBQVU7O3NFQUFPLDhEQUFDc0Q7c0VBQU87Ozs7Ozt3REFBc0I7Ozs7Ozs7OERBQ2xELDhEQUFDakI7b0RBQUVyQyxXQUFVOzhEQUFPOzs7Ozs7OERBQ3BCLDhEQUFDYixrREFBSUE7b0RBQUNXLE1BQUs7b0RBQVNFLFdBQVU7OERBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBV2xFLDhEQUFDbUU7Z0JBQU9uRSxXQUFVOzBCQUNoQiw0RUFBQ0M7b0JBQUlELFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNDO29DQUFJRCxXQUFVOztzREFDYiw4REFBQzhEOzRDQUFHOUQsV0FBVTtzREFBNEI7Ozs7OztzREFDMUMsOERBQUNxQzs0Q0FBRXJDLFdBQVU7c0RBQWtCOzs7Ozs7c0RBQy9CLDhEQUFDcUM7NENBQUVyQyxXQUFVOzs4REFBa0IsOERBQUNzRDs4REFBTzs7Ozs7O2dEQUFlOzs7Ozs7Ozs7Ozs7OzhDQUd4RCw4REFBQ3JEO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ29EOzRDQUFHcEQsV0FBVTtzREFBNEI7Ozs7OztzREFDMUMsOERBQUN5Qjs0Q0FBR3pCLFdBQVU7OzhEQUNaLDhEQUFDMEI7b0RBQUcxQixXQUFVOzhEQUFPLDRFQUFDYixrREFBSUE7d0RBQUNXLE1BQUs7d0RBQUlFLFdBQVU7a0VBQWtDOzs7Ozs7Ozs7Ozs4REFDaEYsOERBQUMwQjtvREFBRzFCLFdBQVU7OERBQU8sNEVBQUNiLGtEQUFJQTt3REFBQ1csTUFBSzt3REFBU0UsV0FBVTtrRUFBa0M7Ozs7Ozs7Ozs7OzhEQUNyRiw4REFBQzBCO29EQUFHMUIsV0FBVTs4REFBTyw0RUFBQ2Isa0RBQUlBO3dEQUFDVyxNQUFLO3dEQUFlRSxXQUFVO2tFQUFrQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSS9GLDhEQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNvRDs0Q0FBR3BELFdBQVU7c0RBQTRCOzs7Ozs7c0RBQzFDLDhEQUFDeUI7NENBQUd6QixXQUFVOzs4REFDWiw4REFBQzBCO29EQUFHMUIsV0FBVTs4REFBTyw0RUFBQ2Isa0RBQUlBO3dEQUFDVyxNQUFLO3dEQUFXRSxXQUFVO2tFQUFrQzs7Ozs7Ozs7Ozs7OERBQ3ZGLDhEQUFDMEI7b0RBQUcxQixXQUFVOzhEQUFPLDRFQUFDYixrREFBSUE7d0RBQUNXLE1BQUs7d0RBQVNFLFdBQVU7a0VBQWtDOzs7Ozs7Ozs7Ozs4REFDckYsOERBQUMwQjtvREFBRzFCLFdBQVU7OERBQU8sNEVBQUNiLGtEQUFJQTt3REFBQ1csTUFBSzt3REFBV0UsV0FBVTtrRUFBa0M7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUkzRiw4REFBQ0M7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDb0Q7NENBQUdwRCxXQUFVO3NEQUE0Qjs7Ozs7O3NEQUMxQyw4REFBQ0M7NENBQUlELFdBQVU7OzhEQUNiLDhEQUFDb0U7b0RBQUV0RSxNQUFLO29EQUFJRSxXQUFVOzhEQUNwQiw0RUFBQ0U7d0RBQUlDLE9BQU07d0RBQUtDLFFBQU87d0RBQUtFLE1BQUs7d0RBQWVELFNBQVE7a0VBQ3RELDRFQUFDTTs0REFBS0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs4REFHWiw4REFBQ3dEO29EQUFFdEUsTUFBSztvREFBSUUsV0FBVTs4REFDcEIsNEVBQUNFO3dEQUFJQyxPQUFNO3dEQUFLQyxRQUFPO3dEQUFLRSxNQUFLO3dEQUFlRCxTQUFRO2tFQUN0RCw0RUFBQ007NERBQUtDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OERBR1osOERBQUN3RDtvREFBRXRFLE1BQUs7b0RBQUlFLFdBQVU7OERBQ3BCLDRFQUFDRTt3REFBSUMsT0FBTTt3REFBS0MsUUFBTzt3REFBS0UsTUFBSzt3REFBZUQsU0FBUTtrRUFDdEQsNEVBQUNNOzREQUFLQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzhEQUdaLDhEQUFDd0Q7b0RBQUV0RSxNQUFLO29EQUFJRSxXQUFVOzhEQUNwQiw0RUFBQ0U7d0RBQUlDLE9BQU07d0RBQUtDLFFBQU87d0RBQUtFLE1BQUs7d0RBQWVELFNBQVE7a0VBQ3RELDRFQUFDTTs0REFBS0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPbEIsOERBQUN5RDs0QkFBR3JFLFdBQVU7Ozs7OztzQ0FFZCw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDQztvQ0FBSUQsV0FBVTs4Q0FDYiw0RUFBQ3FDO3dDQUFFckMsV0FBVTs7NENBQWtCOzBEQUFZLDhEQUFDc0Q7MERBQU87Ozs7Ozs0Q0FBb0I7Ozs7Ozs7Ozs7Ozs4Q0FFekUsOERBQUNyRDtvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNzRTs0Q0FBSUMsS0FBSTs0Q0FBVUMsS0FBSTs0Q0FBVXJFLE9BQU07NENBQUtILFdBQVU7Ozs7OztzREFDdEQsOERBQUNzRTs0Q0FBSUMsS0FBSTs0Q0FBVUMsS0FBSTs0Q0FBUXJFLE9BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPbkQ7S0EvbUJNWjtBQWluQk4sK0RBQWVBLFFBQVFBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL3BhZ2VzL2luZGV4LnRzeD8xOWEwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBIZWFkIGZyb20gJ25leHQvaGVhZCc7XG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IEZpQnJpZWZjYXNlLCBGaVVzZXJzIH0gZnJvbSAncmVhY3QtaWNvbnMvZmknO1xuXG5jb25zdCBIb21lUGFnZTogUmVhY3QuRkMgPSAoKSA9PiB7XG4gIHJldHVybiAoXG4gICAgPD5cbiAgICAgIDxIZWFkPlxuICAgICAgICA8dGl0bGU+RGVhbENsb3NlZCBQYXJ0bmVyIC0gVGhlIHBsYXRmb3JtIHdoZXJlIHNhbGVzIGNvbWUgdG9nZXRoZXI8L3RpdGxlPlxuICAgICAgICA8bWV0YSBuYW1lPVwiZGVzY3JpcHRpb25cIiBjb250ZW50PVwiQXQgRGVhbENsb3NlZCwgd2UgY29ubmVjdCBidXNpbmVzc2VzIHdpdGggZnJlZWxhbmNlIHNhbGVzIGFnZW50cyBpbiBhIHdpbi13aW4gc2l0dWF0aW9uLiBCdXNpbmVzc2VzIGdhaW4gYWNjZXNzIHRvIHRvcCBzYWxlcyBhZ2VudHMsIHdoaWxlIGFnZW50cyBoYXZlIHRoZSBmcmVlZG9tIHRvIHdvcmsgZmxleGlibHnigJRhbnl0aW1lLCBhbnl3aGVyZS5cIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwidmlld3BvcnRcIiBjb250ZW50PVwid2lkdGg9ZGV2aWNlLXdpZHRoLCBpbml0aWFsLXNjYWxlPTFcIiAvPlxuICAgICAgICA8bGluayByZWw9XCJpY29uXCIgaHJlZj1cIi9mYXZpY29uLmljb1wiIC8+XG4gICAgICA8L0hlYWQ+XG5cbiAgICAgIHsvKiBOYXZpZ2F0aW9uIC0gRml4ZWQgc3RpY2t5IG5hdmJhciAqL31cbiAgICAgIDxuYXYgY2xhc3NOYW1lPVwibmF2YmFyIG5hdmJhci1leHBhbmQtbGcgbmF2YmFyLWxpZ2h0IGJnLXdoaXRlIHNoYWRvdy1zbSBweS0zIGZpeGVkLXRvcFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lclwiPlxuICAgICAgICAgIDxMaW5rIGhyZWY9XCIvXCIgY2xhc3NOYW1lPVwibmF2YmFyLWJyYW5kIGQtZmxleCBhbGlnbi1pdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWUtMlwiPlxuICAgICAgICAgICAgICA8c3ZnIHdpZHRoPVwiMzJcIiBoZWlnaHQ9XCIzMlwiIHZpZXdCb3g9XCIwIDAgMTAwIDEwMFwiIGZpbGw9XCJub25lXCI+XG4gICAgICAgICAgICAgICAgPGNpcmNsZSBjeD1cIjUwXCIgY3k9XCI1MFwiIHI9XCI0NVwiIGZpbGw9XCIjMjhhNzQ1XCIvPlxuICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNMzAgNTBMNDUgNjVMNzAgMzVcIiBzdHJva2U9XCJ3aGl0ZVwiIHN0cm9rZVdpZHRoPVwiNlwiIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIi8+XG4gICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmdy1ib2xkIHRleHQtc3VjY2Vzc1wiIHN0eWxlPXt7IGZvbnRTaXplOiAnMS43cmVtJyB9fT5EZWFsQ2xvc2VkPC9zcGFuPlxuICAgICAgICAgIDwvTGluaz5cblxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cIm5hdmJhci10b2dnbGVyXCJcbiAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgZGF0YS1icy10b2dnbGU9XCJjb2xsYXBzZVwiXG4gICAgICAgICAgICBkYXRhLWJzLXRhcmdldD1cIiNuYXZiYXJOYXZcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm5hdmJhci10b2dnbGVyLWljb25cIj48L3NwYW4+XG4gICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbGxhcHNlIG5hdmJhci1jb2xsYXBzZVwiIGlkPVwibmF2YmFyTmF2XCI+XG4gICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwibmF2YmFyLW5hdiBtZS1hdXRvIG1zLTRcIj5cbiAgICAgICAgICAgICAgPGxpIGNsYXNzTmFtZT1cIm5hdi1pdGVtXCI+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9cIiBjbGFzc05hbWU9XCJuYXYtbGluayB0ZXh0LXN1Y2Nlc3NcIiBzdHlsZT17eyBmb250V2VpZ2h0OiAnNTAwJywgZm9udFNpemU6ICcxLjA1cmVtJyB9fT5BdCBob21lPC9MaW5rPlxuICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwibmF2LWl0ZW1cIj5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2Fzc2lnbm1lbnRzXCIgY2xhc3NOYW1lPVwibmF2LWxpbmsgdGV4dC1kYXJrXCIgc3R5bGU9e3sgZm9udFdlaWdodDogJzUwMCcsIGZvbnRTaXplOiAnMS4wNXJlbScgfX0+Q29tbWFuZHM8L0xpbms+XG4gICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJuYXYtaXRlbVwiPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvZnJlZWxhbmNlcnNcIiBjbGFzc05hbWU9XCJuYXYtbGluayB0ZXh0LWRhcmtcIiBzdHlsZT17eyBmb250V2VpZ2h0OiAnNTAwJywgZm9udFNpemU6ICcxLjA1cmVtJyB9fT5EaXNjb3ZlciBmcmVlbGFuY2VyczwvTGluaz5cbiAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgPGxpIGNsYXNzTmFtZT1cIm5hdi1pdGVtXCI+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9jb21wYW5pZXNcIiBjbGFzc05hbWU9XCJuYXYtbGluayB0ZXh0LWRhcmtcIiBzdHlsZT17eyBmb250V2VpZ2h0OiAnNTAwJywgZm9udFNpemU6ICcxLjA1cmVtJyB9fT5GaW5kIGNvbXBhbnk8L0xpbms+XG4gICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJuYXYtaXRlbVwiPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvY291cnNlc1wiIGNsYXNzTmFtZT1cIm5hdi1saW5rIHRleHQtZGFya1wiIHN0eWxlPXt7IGZvbnRXZWlnaHQ6ICc1MDAnLCBmb250U2l6ZTogJzEuMDVyZW0nIH19PkNvdXJzZXM8L0xpbms+XG4gICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJuYXYtaXRlbVwiPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvcmFua2luZ1wiIGNsYXNzTmFtZT1cIm5hdi1saW5rIHRleHQtZGFya1wiIHN0eWxlPXt7IGZvbnRXZWlnaHQ6ICc1MDAnLCBmb250U2l6ZTogJzEuMDVyZW0nIH19PlJhbmtpbmdzPC9MaW5rPlxuICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwibmF2LWl0ZW1cIj5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2Jsb2dcIiBjbGFzc05hbWU9XCJuYXYtbGluayB0ZXh0LWRhcmtcIiBzdHlsZT17eyBmb250V2VpZ2h0OiAnNTAwJywgZm9udFNpemU6ICcxLjA1cmVtJyB9fT5CbG9nczwvTGluaz5cbiAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgIDwvdWw+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZC1mbGV4IGdhcC0yIGFsaWduLWl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2xvZ2luXCIgY2xhc3NOYW1lPVwibmF2LWxpbmsgdGV4dC1kYXJrXCIgc3R5bGU9e3sgZm9udFdlaWdodDogJzUwMCcsIGZvbnRTaXplOiAnMS4wNXJlbScgfX0+XG4gICAgICAgICAgICAgICAgTG9naW5cbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL3JlZ2lzdGVyXCIgY2xhc3NOYW1lPVwiYnRuIGJ0bi1zdWNjZXNzIHJvdW5kZWQtcGlsbCBweC00IHB5LTJcIiBzdHlsZT17eyBmb250V2VpZ2h0OiAnNTAwJywgZm9udFNpemU6ICcxNXB4JyB9fT5cbiAgICAgICAgICAgICAgICBHZXQgc3RhcnRlZFxuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L25hdj5cblxuICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicG9zaXRpb24tcmVsYXRpdmVcIiBzdHlsZT17e1xuICAgICAgICBiYWNrZ3JvdW5kSW1hZ2U6ICd1cmwoL2hlcm8tYmcuanBnKScsXG4gICAgICAgIGJhY2tncm91bmRTaXplOiAnY292ZXInLFxuICAgICAgICBiYWNrZ3JvdW5kUG9zaXRpb246ICdjZW50ZXInLFxuICAgICAgICBtaW5IZWlnaHQ6ICcxMDB2aCcsXG4gICAgICAgIG1hcmdpblRvcDogJzgwcHgnXG4gICAgICB9fT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgaC0xMDAgZC1mbGV4IGFsaWduLWl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicm93IHctMTAwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1sZy04IHRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cImRpc3BsYXktMyBmdy1ib2xkIG1iLTRcIiBzdHlsZT17eyBmb250U2l6ZTogJzMuNXJlbScsIGxpbmVIZWlnaHQ6ICcxLjInIH19PlxuICAgICAgICAgICAgICAgIFdlbGNvbWUgdG8gRGVhbCBDbG9zZWQsPGJyIC8+XG4gICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9e3sgZm9udFNpemU6ICcyLjhyZW0nIH19PnRoZSBwbGF0Zm9ybSB3aGVyZSBzYWxlcyBjb21lIHRvZ2V0aGVyPC9zcGFuPlxuICAgICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJsZWFkIG1iLTRcIiBzdHlsZT17eyBmb250U2l6ZTogJzEuMjVyZW0nLCBvcGFjaXR5OiAnMC45NScsIG1heFdpZHRoOiAnNjAwcHgnIH19PlxuICAgICAgICAgICAgICAgIEF0IERlYWxDbG9zZWQsIHdlIGNvbm5lY3QgY29tcGFuaWVzIHdpdGggZnJlZWxhbmNlIHNhbGVzIGFnZW50cyBpbiBhIHdpbi13aW4gc2l0dWF0aW9uLiBCdXNpbmVzc2VzIGdldCBhY2Nlc3MgdG9cbiAgICAgICAgICAgICAgICB0b3Agc2FsZXMgYWdlbnRzLCB3aGlsZSBhZ2VudHMgaGF2ZSB0aGUgZnJlZWRvbSB0byB3b3JrIGZsZXhpYmx5IOKAlCBhbnl0aW1lLCBhbnl3aGVyZS5cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuIGJ0bi1sZyBweC01IHB5LTMgcm91bmRlZC1waWxsXCJcbiAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAndHJhbnNwYXJlbnQnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnMnB4IHNvbGlkIHdoaXRlJyxcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxNnB4JyxcbiAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc0MDAnLFxuICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogJ2FsbCAwLjNzIGVhc2UnXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICBvbk1vdXNlRW50ZXI9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICBjb25zdCB0YXJnZXQgPSBlLnRhcmdldCBhcyBIVE1MQnV0dG9uRWxlbWVudDtcbiAgICAgICAgICAgICAgICAgIHRhcmdldC5zdHlsZS5iYWNrZ3JvdW5kQ29sb3IgPSAnIzI4YTc0NSc7XG4gICAgICAgICAgICAgICAgICB0YXJnZXQuc3R5bGUuYm9yZGVyQ29sb3IgPSAnIzI4YTc0NSc7XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICBvbk1vdXNlTGVhdmU9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICBjb25zdCB0YXJnZXQgPSBlLnRhcmdldCBhcyBIVE1MQnV0dG9uRWxlbWVudDtcbiAgICAgICAgICAgICAgICAgIHRhcmdldC5zdHlsZS5iYWNrZ3JvdW5kQ29sb3IgPSAndHJhbnNwYXJlbnQnO1xuICAgICAgICAgICAgICAgICAgdGFyZ2V0LnN0eWxlLmJvcmRlckNvbG9yID0gJ3doaXRlJztcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9yZWdpc3Rlcid9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBHZXQgc3RhcnRlZFxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgey8qIENvbm5lY3RpbmcgQ29tcGFuaWVzIFNlY3Rpb24gKi99XG4gICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS0zXCIgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiAnI2Y4ZjlmYScgfX0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPGg2IGNsYXNzTmFtZT1cIm1iLTAgdGV4dC1tdXRlZFwiIHN0eWxlPXt7IGZvbnRTaXplOiAnMC45cmVtJyB9fT5Db25uZWN0aW5nIGNvbXBhbmllcyB3aXRoIGZyZWVsYW5jZSBzYWxlcyBhc3NvY2lhdGVzPC9oNj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L3NlY3Rpb24+XG5cbiAgICAgIHsvKiBNYWluIERlc2NyaXB0aW9uIFNlY3Rpb24gKi99XG4gICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS01IGJnLXdoaXRlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyb3dcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLWxnLTEyXCI+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJmdy1ib2xkIG1iLTRcIiBzdHlsZT17eyBmb250U2l6ZTogJzIuNXJlbScsIGNvbG9yOiAnIzMzMycgfX0+XG4gICAgICAgICAgICAgICAgQ29ubmVjdGluZyBjb21wYW5pZXMgd2l0aCBmcmVlbGFuY2Ugc2FsZXMgYWdlbnRzIHdobyBkZWxpdmVyLlxuICAgICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtYi0zXCIgc3R5bGU9e3sgZm9udFNpemU6ICcxLjFyZW0nLCBsaW5lSGVpZ2h0OiAnMS42JyB9fT5cbiAgICAgICAgICAgICAgICA8c3Ryb25nPkRlYWxDbG9zZWQ8L3N0cm9uZz4gaXMgdGhlIHBsYXRmb3JtIHdoZXJlIGNvbXBhbmllcyBhbmQgZnJlZWxhbmNlIHNhbGVzIGFnZW50cyBtZWV0LlxuICAgICAgICAgICAgICAgIE5vIGV4cGVuc2l2ZSBjb250cmFjdHMgb3IgcmVjcnVpdG1lbnQgYWdlbmNpZXMg4oCTIGp1c3QgY29sbGFib3JhdGlvbiBiYXNlZCBvbiBwZXJmb3JtYW5jZS5cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtYi0zXCIgc3R5bGU9e3sgZm9udFNpemU6ICcxLjFyZW0nLCBsaW5lSGVpZ2h0OiAnMS42JyB9fT5cbiAgICAgICAgICAgICAgICBXaGV0aGVyIHlvdSdyZSBsb29raW5nIHRvIGNsb3NlIGRlYWxzIG9yIGZpbmQgc29tZW9uZSB3aG8gY2FuIGRvIGl0IGZvciB5b3UsXG4gICAgICAgICAgICAgICAgRGVhbENsb3NlZCBpcyB0aGUgcGxhY2UgdG8gYmUuXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPHAgc3R5bGU9e3sgZm9udFNpemU6ICcxLjFyZW0nLCBsaW5lSGVpZ2h0OiAnMS42JywgY29sb3I6ICcjNjY2JywgZm9udFN0eWxlOiAnaXRhbGljJyB9fT5cbiAgICAgICAgICAgICAgICBXb3JrIGZsZXhpYmx5LiBFYXJuIGZhaXJseS4gR3JvdyB0b2dldGhlci5cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICB7LyogQmVuZWZpdHMgU2VjdGlvbiAtIEV4YWN0IG1hdGNoIHRvIHlvdXIgZGVzaWduICovfVxuICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicHktNSBiZy1saWdodFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicm93IGctNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtbGctNlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQgaC0xMDAgYm9yZGVyLTAgc2hhZG93LXNtIHBvc2l0aW9uLXJlbGF0aXZlXCIgc3R5bGU9e3sgYm9yZGVyUmFkaXVzOiAnMjBweCcsIG1pbkhlaWdodDogJzM1MHB4JyB9fT5cbiAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwb3NpdGlvbi1hYnNvbHV0ZSB0b3AtMCBzdGFydC0wIGgtMTAwXCJcbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgIHdpZHRoOiAnOHB4JyxcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnIzI4YTc0NScsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlclRvcExlZnRSYWRpdXM6ICcyMHB4JyxcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyQm90dG9tTGVmdFJhZGl1czogJzIwcHgnXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgID48L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQtYm9keSBwLTVcIiBzdHlsZT17eyBwYWRkaW5nTGVmdDogJzNyZW0nIH19PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc3VjY2VzcyBiZy1vcGFjaXR5LTEwIHJvdW5kZWQtY2lyY2xlIGQtaW5saW5lLWZsZXggYWxpZ24taXRlbXMtY2VudGVyIGp1c3RpZnktY29udGVudC1jZW50ZXJcIiBzdHlsZT17eyB3aWR0aDogJzgwcHgnLCBoZWlnaHQ6ICc4MHB4JyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICA8c3ZnIHdpZHRoPVwiNDBcIiBoZWlnaHQ9XCI0MFwiIGZpbGw9XCIjMjhhNzQ1XCIgdmlld0JveD1cIjAgMCAxNiAxNlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZD1cIk03IDE0cy0xIDAtMS0xIDEtNCA1LTQgNSAzIDUgNC0xIDEtMSAxSDd6bTQtNmEzIDMgMCAxIDAgMC02IDMgMyAwIDAgMCAwIDZ6XCIvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk01LjIxNiAxNEEyLjIzOCAyLjIzOCAwIDAgMSA1IDEzYzAtMS4zNTUuNjgtMi43NSAxLjkzNi0zLjcyQTYuMzI1IDYuMzI1IDAgMCAwIDUgOWMtNCAwLTUgMy01IDRzMSAxIDEgMWg0LjIxNnpcIi8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTQuNSA4YTIuNSAyLjUgMCAxIDAgMC01IDIuNSAyLjUgMCAwIDAgMCA1elwiLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBmdy1ib2xkIG1iLTRcIiBzdHlsZT17eyBmb250U2l6ZTogJzEuNHJlbScsIGNvbG9yOiAnIzY2NicgfX0+QmVuZWZpdHMgZm9yIGZyZWVsYW5jZSBzYWxlcyBhc3NvY2lhdGVzPC9oND5cbiAgICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJsaXN0LXVuc3R5bGVkXCIgc3R5bGU9e3sgZm9udFNpemU6ICcxcmVtJywgbGluZUhlaWdodDogJzEuOCcgfX0+XG4gICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJtYi0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAg4oCiIDxzdHJvbmc+V29yayB3aGVyZXZlciB5b3Ugd2FudDwvc3Ryb25nPuKAlGF0IGhvbWUsIGluIGEgY2Fmw6ksIG9yIG9uIHRoZSBnb+KAlHlvdVxuICAgICAgICAgICAgICAgICAgICAgIGNob29zZSB3aGVuIGFuZCB3aGVyZSB5b3Ugd29yay5cbiAgICAgICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICAgICAgPGxpIGNsYXNzTmFtZT1cIm1iLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICDigKIgPHN0cm9uZz5FYXJuIGF0dHJhY3RpdmUgY29tbWlzc2lvbnM6PC9zdHJvbmc+IEdldCBhIGZhaXIgZmVlIHBlciBkZWFsIGNsb3NlZC4gWW91clxuICAgICAgICAgICAgICAgICAgICAgIHN1Y2Nlc3MgaXMgaW4geW91ciBoYW5kcy5cbiAgICAgICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICAgICAgPGxpIGNsYXNzTmFtZT1cIm1iLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICDigKIgPHN0cm9uZz5EaXZlcnNlIG9wcG9ydHVuaXRpZXM6PC9zdHJvbmc+IGRpc2NvdmVyIGEgd2lkZSByYW5nZSBvZiBwcm9qZWN0cyBhbmRcbiAgICAgICAgICAgICAgICAgICAgICBjb21wYW5pZXMgdGhhdCBtYXRjaCB5b3VyIHNraWxscyBhbmQgaW50ZXJlc3RzXG4gICAgICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtbGctNlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQgaC0xMDAgYm9yZGVyLTAgc2hhZG93LXNtIHBvc2l0aW9uLXJlbGF0aXZlXCIgc3R5bGU9e3sgYm9yZGVyUmFkaXVzOiAnMjBweCcsIG1pbkhlaWdodDogJzM1MHB4JyB9fT5cbiAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwb3NpdGlvbi1hYnNvbHV0ZSB0b3AtMCBzdGFydC0wIGgtMTAwXCJcbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgIHdpZHRoOiAnOHB4JyxcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnIzI4YTc0NScsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlclRvcExlZnRSYWRpdXM6ICcyMHB4JyxcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyQm90dG9tTGVmdFJhZGl1czogJzIwcHgnXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgID48L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQtYm9keSBwLTVcIiBzdHlsZT17eyBwYWRkaW5nTGVmdDogJzNyZW0nIH19PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc3VjY2VzcyBiZy1vcGFjaXR5LTEwIHJvdW5kZWQtY2lyY2xlIGQtaW5saW5lLWZsZXggYWxpZ24taXRlbXMtY2VudGVyIGp1c3RpZnktY29udGVudC1jZW50ZXJcIiBzdHlsZT17eyB3aWR0aDogJzgwcHgnLCBoZWlnaHQ6ICc4MHB4JyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICA8c3ZnIHdpZHRoPVwiNDBcIiBoZWlnaHQ9XCI0MFwiIGZpbGw9XCIjMjhhNzQ1XCIgdmlld0JveD1cIjAgMCAxNiAxNlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZD1cIk04IDQuNzU0YTMuMjQ2IDMuMjQ2IDAgMSAwIDAgNi40OTIgMy4yNDYgMy4yNDYgMCAwIDAgMC02LjQ5MnpNNS43NTQgOGEyLjI0NiAyLjI0NiAwIDEgMSA0LjQ5MiAwIDIuMjQ2IDIuMjQ2IDAgMCAxLTQuNDkyIDB6XCIvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZD1cIk05Ljc5NiAxLjM0M2MtLjUyNy0xLjc5LTMuMDY1LTEuNzktMy41OTIgMGwtLjA5NC4zMTlhLjg3My44NzMgMCAwIDEtMS4yNTUuNTJsLS4yOTItLjE2Yy0xLjY0LS44OTItMy40MzMuOTAyLTIuNTQgMi41NDFsLjE1OS4yOTJhLjg3My44NzMgMCAwIDEtLjUyIDEuMjU1bC0uMzE5LjA5NGMtMS43OS41MjctMS43OSAzLjA2NSAwIDMuNTkybC4zMTkuMDk0YS44NzMuODczIDAgMCAxIC41MiAxLjI1NWwtLjE2LjI5MmMtLjg5MiAxLjY0LjkwMSAzLjQzNCAyLjU0MSAyLjU0bC4yOTItLjE1OWEuODczLjg3MyAwIDAgMSAxLjI1NS41MmwuMDk0LjMxOWMuNTI3IDEuNzkgMy4wNjUgMS43OSAzLjU5MiAwbC4wOTQtLjMxOWEuODczLjg3MyAwIDAgMSAxLjI1NS0uNTJsLjI5Mi4xNmMxLjY0Ljg5MyAzLjQzNC0uOTAyIDIuNTQtMi41NDFsLS4xNTktLjI5MmEuODczLjg3MyAwIDAgMSAuNTItMS4yNTVsLjMxOS0uMDk0YzEuNzktLjUyNyAxLjc5LTMuMDY1IDAtMy41OTJsLS4zMTktLjA5NGEuODczLjg3MyAwIDAgMS0uNTItMS4yNTVsLjE2LS4yOTJjLjg5My0xLjY0LS45MDItMy40MzMtMi41NDEtMi41NGwtLjI5Mi4xNTlhLjg3My44NzMgMCAwIDEtMS4yNTUtLjUybC0uMDk0LS4zMTl6XCIvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIGZ3LWJvbGQgbWItNFwiIHN0eWxlPXt7IGZvbnRTaXplOiAnMS40cmVtJywgY29sb3I6ICcjNjY2JyB9fT5CZW5lZml0cyBmb3IgY29tcGFuaWVzPC9oND5cbiAgICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJsaXN0LXVuc3R5bGVkXCIgc3R5bGU9e3sgZm9udFNpemU6ICcxcmVtJywgbGluZUhlaWdodDogJzEuOCcgfX0+XG4gICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJtYi0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAg4oCiIDxzdHJvbmc+TW9yZSByZXZlbnVlLCBsZXNzIHJpc2s8L3N0cm9uZz7igJRvbmx5IHBheSBmb3IgcmVzdWx0cy4gTm8gZml4ZWQgc2FsYXJpZXMgb3JcbiAgICAgICAgICAgICAgICAgICAgICByZWNydWl0bWVudCBjb3N0cyDigJMganVzdCBhIGNsZWFyIGNvbW1pc3Npb24gcGVyIGRlYWwgY2xvc2VkLlxuICAgICAgICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwibWItM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIOKAoiA8c3Ryb25nPkFjY2VzcyB0byBza2lsbGVkIHNhbGVzIHJlcHM6PC9zdHJvbmc+IENvbm5lY3Qgd2l0aCBtb3RpdmF0ZWQgZnJlZWxhbmNlcnMgd2hvXG4gICAgICAgICAgICAgICAgICAgICAgYXJlIHJlYWR5IHRvIGdyb3cgeW91ciBidXNpbmVzcy5cbiAgICAgICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICAgICAgPGxpIGNsYXNzTmFtZT1cIm1iLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICDigKIgPHN0cm9uZz5RdWljayBhbmQgZWFzeSBjb2xsYWJvcmF0aW9uOjwvc3Ryb25nPiBQb3N0IHlvdXIgYnJpZWYsIHNlbGVjdCB0aGUgcmlnaHQgc2FsZXNcbiAgICAgICAgICAgICAgICAgICAgICBhZ2VudCwgYW5kIGxldCB0aGUgZGVhbHMgZmxvdy5cbiAgICAgICAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICB7LyogV2h5IFdlIERvIFdoYXQgV2UgRG8gU2VjdGlvbiAqL31cbiAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInB5LTUgYmctd2hpdGVcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJvdyBhbGlnbi1pdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLWxnLTZcIj5cbiAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cImZ3LWJvbGQgbWItNFwiIHN0eWxlPXt7IGZvbnRTaXplOiAnMi41cmVtJywgY29sb3I6ICcjMzMzJyB9fT5XaHkgd2UgZG8gd2hhdCB3ZSBkbzwvaDI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQgbWItNFwiIHN0eWxlPXt7IGZvbnRTaXplOiAnMXJlbScsIGxpbmVIZWlnaHQ6ICcxLjYnLCBmb250U3R5bGU6ICdpdGFsaWMnIH19PlxuICAgICAgICAgICAgICAgIEF0IERlYWxDbG9zZWQsIHdlIGJlbGlldmUgaW4gc29sdmluZyByZWFsIHNhbGVzIGNoYWxsZW5nZXMgd2l0aCBzaW1wbGUsIGVmZmVjdGl2ZSBzb2x1dGlvbnMuXG4gICAgICAgICAgICAgICAgT3VyIHBsYXRmb3JtIGhhcyBiZWVuIGRldmVsb3BlZCBpbiByZXNwb25zZSB0byBhIGNsZWFyIG5lZWQgaW4gdGhlIG1hcmtldDpcbiAgICAgICAgICAgICAgPC9wPlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZC1mbGV4IGFsaWduLWl0ZW1zLXN0YXJ0IG1iLTNcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWUtM1wiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXN1Y2Nlc3NcIiBzdHlsZT17eyBmb250U2l6ZTogJzEuMnJlbScgfX0+4pyTPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDUgY2xhc3NOYW1lPVwibWItMlwiIHN0eWxlPXt7IGZvbnRTaXplOiAnMS4xcmVtJywgZm9udFdlaWdodDogJzYwMCcgfX0+Rm9yIGJ1c2luZXNzZXM6PC9oNT5cbiAgICAgICAgICAgICAgICAgICAgPHAgc3R5bGU9e3sgZm9udFNpemU6ICcwLjk1cmVtJywgbGluZUhlaWdodDogJzEuNicsIG1hcmdpbjogJzAnIH19PlxuICAgICAgICAgICAgICAgICAgICAgIE1hbnkgY29tcGFuaWVzIHN0cnVnZ2xlIHRvIGZpbmQgcmVsaWFibGUgc2FsZXMgdGFsZW50IHdpdGhvdXQgbG9uZy10ZXJtIGNvbnRyYWN0cyBvciBoaWdoIGhpcmluZyBjb3N0cy5cbiAgICAgICAgICAgICAgICAgICAgICBUaGV5IG5lZWQgZmxleGlibGUsIHJlc3VsdHMtZHJpdmVuIHNvbHV0aW9ucyB0byBncm93IHRoZWlyIHJldmVudWUgcmlzay1mcmVlLlxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJkLWZsZXggYWxpZ24taXRlbXMtc3RhcnQgbWItM1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtZS0zXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc3VjY2Vzc1wiIHN0eWxlPXt7IGZvbnRTaXplOiAnMS4ycmVtJyB9fT7inJM8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxoNSBjbGFzc05hbWU9XCJtYi0yXCIgc3R5bGU9e3sgZm9udFNpemU6ICcxLjFyZW0nLCBmb250V2VpZ2h0OiAnNjAwJyB9fT5Gb3Igc2FsZXMgYWdlbnRzOjwvaDU+XG4gICAgICAgICAgICAgICAgICAgIDxwIHN0eWxlPXt7IGZvbnRTaXplOiAnMC45NXJlbScsIGxpbmVIZWlnaHQ6ICcxLjYnLCBtYXJnaW46ICcwJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICBUYWxlbnRlZCBwZW9wbGUgb2Z0ZW4gbGFjayB0aGUgZnJlZWRvbSB0byB3b3JrIG9uIHRoZWlyIG93biB0ZXJtcyBvciBmaW5kIG9wcG9ydHVuaXRpZXMgdGhhdCBtYXRjaCB0aGVpciB1bmlxdWUgc2tpbGwgc2V0cy5cbiAgICAgICAgICAgICAgICAgICAgICBNYW55IGFyZSBzdHVjayBpbiByaWdpZCB3b3JraW5nIG1vZGVscyB0aGF0IGxpbWl0IHRoZWlyIHBvdGVudGlhbC4gV2UgZ2l2ZSB0aGVtIHRoZSBvcHBvcnR1bml0eSB0byB3b3JrIGZyZWVseSxcbiAgICAgICAgICAgICAgICAgICAgICB3aGVyZXZlciB0aGV5IGFyZSwgYW5kIGFjaGlldmUgc3VjY2VzcyBiYXNlZCBvbiBwZXJmb3JtYW5jZS5cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJidG4gYnRuLXN1Y2Nlc3Mgcm91bmRlZC1waWxsIHB4LTQgcHktMlwiXG4gICAgICAgICAgICAgICAgc3R5bGU9e3sgZm9udFNpemU6ICcwLjlyZW0nLCBmb250V2VpZ2h0OiAnNDAwJyB9fVxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9hYm91dCd9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBSZWFkIG1vcmUg4oaSXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1sZy02XCI+XG4gICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkIHNoYWRvd1wiXG4gICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgIGhlaWdodDogJzQwMHB4JyxcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRJbWFnZTogJ3VybCgvYWJvdXQuanBnKScsXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kU2l6ZTogJ2NvdmVyJyxcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRQb3NpdGlvbjogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kUmVwZWF0OiAnbm8tcmVwZWF0J1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgID48L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgey8qIEJlbmVmaXRzIFNlY3Rpb24gLSBFeGFjdCBtYXRjaCB0byBkZWFsY2xvc2VkcGFydG5lci5ubCAqL31cbiAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInB5LTUgYmctbGlnaHRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJvdyBnLTRcIj5cbiAgICAgICAgICAgIHsvKiBCZW5lZml0cyBmb3IgZnJlZWxhbmNlIHNhbGVzIGFzc29jaWF0ZXMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1sZy02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZCBoLTEwMCBib3JkZXItMCBzaGFkb3ctc20gcG9zaXRpb24tcmVsYXRpdmVcIiBzdHlsZT17eyBib3JkZXJSYWRpdXM6ICcxNXB4JyB9fT5cbiAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwb3NpdGlvbi1hYnNvbHV0ZSB0b3AtMCBzdGFydC0wIGgtMTAwXCJcbiAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgIHdpZHRoOiAnNnB4JyxcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnIzI4YTc0NScsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlclRvcExlZnRSYWRpdXM6ICcxNXB4JyxcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyQm90dG9tTGVmdFJhZGl1czogJzE1cHgnXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgID48L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQtYm9keSBwLTRcIiBzdHlsZT17eyBwYWRkaW5nTGVmdDogJzJyZW0nIH19PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJkLWZsZXggYWxpZ24taXRlbXMtY2VudGVyIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1zdWNjZXNzIGJnLW9wYWNpdHktMTAgcm91bmRlZC1jaXJjbGUgcC0zIG1lLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8RmlVc2VycyBjbGFzc05hbWU9XCJ0ZXh0LXN1Y2Nlc3NcIiBzaXplPXsyNH0gLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJtYi0wIGZ3LWJvbGRcIiBzdHlsZT17eyBmb250U2l6ZTogJzEuM3JlbScsIGNvbG9yOiAnIzY2NicgfX0+QmVuZWZpdHMgZm9yIGZyZWVsYW5jZSBzYWxlcyBhc3NvY2lhdGVzPC9oMz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwibGlzdC11bnN0eWxlZFwiIHN0eWxlPXt7IGZvbnRTaXplOiAnMC45cmVtJywgbGluZUhlaWdodDogJzEuNicgfX0+XG4gICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJtYi0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHN0cm9uZz5Xb3JrIHdoZXJldmVyIHlvdSB3YW50PC9zdHJvbmc+4oCUYXQgaG9tZSwgaW4gYSBjYWbDqSwgb3Igb24gdGhlIGdv4oCUeW91XG4gICAgICAgICAgICAgICAgICAgICAgY2hvb3NlIHdoZW4gYW5kIHdoZXJlIHlvdSB3b3JrLlxuICAgICAgICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwibWItM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzdHJvbmc+RWFybiBhdHRyYWN0aXZlIGNvbW1pc3Npb25zOjwvc3Ryb25nPiBHZXQgYSBmYWlyIGZlZSBwZXIgZGVhbCBjbG9zZWQuIFlvdXJcbiAgICAgICAgICAgICAgICAgICAgICBzdWNjZXNzIGlzIGluIHlvdXIgaGFuZHMuXG4gICAgICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJtYi0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHN0cm9uZz5EaXZlcnNlIG9wcG9ydHVuaXRpZXM6PC9zdHJvbmc+IGRpc2NvdmVyIGEgd2lkZSByYW5nZSBvZiBwcm9qZWN0cyBhbmRcbiAgICAgICAgICAgICAgICAgICAgICBjb21wYW5pZXMgdGhhdCBtYXRjaCB5b3VyIHNraWxscyBhbmQgaW50ZXJlc3RzXG4gICAgICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogQmVuZWZpdHMgZm9yIGNvbXBhbmllcyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLWxnLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkIGgtMTAwIGJvcmRlci0wIHNoYWRvdy1zbSBwb3NpdGlvbi1yZWxhdGl2ZVwiIHN0eWxlPXt7IGJvcmRlclJhZGl1czogJzE1cHgnIH19PlxuICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInBvc2l0aW9uLWFic29sdXRlIHRvcC0wIHN0YXJ0LTAgaC0xMDBcIlxuICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgd2lkdGg6ICc2cHgnLFxuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjMjhhNzQ1JyxcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyVG9wTGVmdFJhZGl1czogJzE1cHgnLFxuICAgICAgICAgICAgICAgICAgICBib3JkZXJCb3R0b21MZWZ0UmFkaXVzOiAnMTVweCdcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgPjwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZC1ib2R5IHAtNFwiIHN0eWxlPXt7IHBhZGRpbmdMZWZ0OiAnMnJlbScgfX0+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImQtZmxleCBhbGlnbi1pdGVtcy1jZW50ZXIgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXN1Y2Nlc3MgYmctb3BhY2l0eS0xMCByb3VuZGVkLWNpcmNsZSBwLTMgbWUtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxGaUJyaWVmY2FzZSBjbGFzc05hbWU9XCJ0ZXh0LXN1Y2Nlc3NcIiBzaXplPXsyNH0gLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJtYi0wIGZ3LWJvbGRcIiBzdHlsZT17eyBmb250U2l6ZTogJzEuM3JlbScsIGNvbG9yOiAnIzY2NicgfX0+QmVuZWZpdHMgZm9yIGNvbXBhbmllczwvaDM+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cImxpc3QtdW5zdHlsZWRcIiBzdHlsZT17eyBmb250U2l6ZTogJzAuOXJlbScsIGxpbmVIZWlnaHQ6ICcxLjYnIH19PlxuICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwibWItM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzdHJvbmc+TW9yZSByZXZlbnVlLCBsZXNzIHJpc2s8L3N0cm9uZz7igJRvbmx5IHBheSBmb3IgcmVzdWx0cy4gTm8gZml4ZWQgc2FsYXJpZXMgb3JcbiAgICAgICAgICAgICAgICAgICAgICByZWNydWl0bWVudCBjb3N0cyDigJMganVzdCBhIGNsZWFyIGNvbW1pc3Npb24gcGVyIGRlYWwgY2xvc2VkLlxuICAgICAgICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwibWItM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzdHJvbmc+QWNjZXNzIHRvIHNraWxsZWQgc2FsZXMgcmVwczo8L3N0cm9uZz4gQ29ubmVjdCB3aXRoIG1vdGl2YXRlZCBmcmVlbGFuY2VycyB3aG9cbiAgICAgICAgICAgICAgICAgICAgICBhcmUgcmVhZHkgdG8gZ3JvdyB5b3VyIGJ1c2luZXNzLlxuICAgICAgICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwibWItM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzdHJvbmc+UXVpY2sgYW5kIGVhc3kgY29sbGFib3JhdGlvbjo8L3N0cm9uZz4gUG9zdCB5b3VyIGJyaWVmLCBzZWxlY3QgdGhlIHJpZ2h0IHNhbGVzXG4gICAgICAgICAgICAgICAgICAgICAgYWdlbnQsIGFuZCBsZXQgdGhlIGRlYWxzIGZsb3cuXG4gICAgICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvc2VjdGlvbj5cbiAgICAgIHsvKiBXaHkgY2hvb3NlIHVzIHNlY3Rpb24gLSBFeGFjdCBtYXRjaCB0byB5b3VyIGRlc2lnbiAqL31cbiAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInB5LTVcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJvdyBnLTRcIj5cbiAgICAgICAgICAgIHsvKiBHcmVlbiBjYXJkIC0gV2h5IGNob29zZSB1cyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLWxnLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkIGgtMTAwIGJvcmRlci0wIHNoYWRvdy1zbSB0ZXh0LXdoaXRlXCIgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiAnIzI4YTc0NScsIGJvcmRlclJhZGl1czogJzIwcHgnLCBtaW5IZWlnaHQ6ICczMDBweCcgfX0+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkLWJvZHkgcC01XCI+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZnctYm9sZCBtYi00XCIgc3R5bGU9e3sgZm9udFNpemU6ICcxLjhyZW0nIH19PldoeSBzaG91bGQgeW91IGNob29zZSB1cz88L2gzPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibWItNFwiIHN0eWxlPXt7IGZvbnRTaXplOiAnMXJlbScsIGxpbmVIZWlnaHQ6ICcxLjYnIH19PlxuICAgICAgICAgICAgICAgICAgICBUbyBicmlkZ2UgdGhpcyBnYXAsIHdlIGZvdW5kZWQgRGVhbENsb3NlZC4gQnkgbGlua2luZyBjb21wYW5pZXMgd2l0aCBtb3RpdmF0ZWQgZnJlZWxhbmNlXG4gICAgICAgICAgICAgICAgICAgIHNhbGVzIHN0YWZmLCB3ZSBvZmZlciBhIHdpbi13aW4gc2l0dWF0aW9uOiBjb21wYW5pZXMgYWNoaWV2ZSByZXN1bHRzIHdpdGhvdXQgdW5uZWNlc3NhcnlcbiAgICAgICAgICAgICAgICAgICAgb3ZlcmhlYWQgYW5kIHNhbGVzIGVtcGxveWVlcyBnZXQgdGhlIGZsZXhpYmlsaXR5IHRvIHdvcmsgaG93LCB3aGVuIGFuZCB3aGVyZSB0aGV5IHdhbnQuXG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJ0biBweC00IHB5LTIgcm91bmRlZC1waWxsXCJcbiAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjMWU3ZTM0JyxcbiAgICAgICAgICAgICAgICAgICAgICBib3JkZXI6ICdub25lJyxcbiAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzAuOXJlbScsXG4gICAgICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogJzQwMCcsXG4gICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogJ2FsbCAwLjNzIGVhc2UnXG4gICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgIG9uTW91c2VFbnRlcj17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICBjb25zdCB0YXJnZXQgPSBlLnRhcmdldCBhcyBIVE1MQnV0dG9uRWxlbWVudDtcbiAgICAgICAgICAgICAgICAgICAgICB0YXJnZXQuc3R5bGUuYmFja2dyb3VuZENvbG9yID0gJ3doaXRlJztcbiAgICAgICAgICAgICAgICAgICAgICB0YXJnZXQuc3R5bGUuY29sb3IgPSAnIzI4YTc0NSc7XG4gICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgIG9uTW91c2VMZWF2ZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICBjb25zdCB0YXJnZXQgPSBlLnRhcmdldCBhcyBIVE1MQnV0dG9uRWxlbWVudDtcbiAgICAgICAgICAgICAgICAgICAgICB0YXJnZXQuc3R5bGUuYmFja2dyb3VuZENvbG9yID0gJyMxZTdlMzQnO1xuICAgICAgICAgICAgICAgICAgICAgIHRhcmdldC5zdHlsZS5jb2xvciA9ICd3aGl0ZSc7XG4gICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9hYm91dCd9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIExlYXJuIG1vcmUg4oaSXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFRyYW5zcGFyZW5jeSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLWxnLTJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkIGgtMTAwIGJvcmRlci0wIHNoYWRvdy1zbSBwb3NpdGlvbi1yZWxhdGl2ZVwiIHN0eWxlPXt7IGJvcmRlclJhZGl1czogJzIwcHgnLCBtaW5IZWlnaHQ6ICczMDBweCcgfX0+XG4gICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicG9zaXRpb24tYWJzb2x1dGUgdG9wLTAgc3RhcnQtMCBoLTEwMFwiXG4gICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzhweCcsXG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJyMyOGE3NDUnLFxuICAgICAgICAgICAgICAgICAgICBib3JkZXJUb3BMZWZ0UmFkaXVzOiAnMjBweCcsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlckJvdHRvbUxlZnRSYWRpdXM6ICcyMHB4J1xuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICA+PC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkLWJvZHkgcC00IHRleHQtY2VudGVyIGQtZmxleCBmbGV4LWNvbHVtbiBqdXN0aWZ5LWNvbnRlbnQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1zdWNjZXNzIGJnLW9wYWNpdHktMTAgcm91bmRlZC1jaXJjbGUgZC1pbmxpbmUtZmxleCBhbGlnbi1pdGVtcy1jZW50ZXIganVzdGlmeS1jb250ZW50LWNlbnRlclwiIHN0eWxlPXt7IHdpZHRoOiAnNjBweCcsIGhlaWdodDogJzYwcHgnIH19PlxuICAgICAgICAgICAgICAgICAgICAgIDxzdmcgd2lkdGg9XCIzMFwiIGhlaWdodD1cIjMwXCIgZmlsbD1cIiMyOGE3NDVcIiB2aWV3Qm94PVwiMCAwIDE2IDE2XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTggMWEyLjUgMi41IDAgMCAxIDIuNSAyLjVWNGgtNXYtLjVBMi41IDIuNSAwIDAgMSA4IDF6bTMuNSAzdi0uNWEzLjUgMy41IDAgMSAwLTcgMFY0SDF2MTBhMiAyIDAgMCAwIDIgMmgxMGEyIDIgMCAwIDAgMi0yVjRoLTMuNXpNMiA1aDEydjlhMSAxIDAgMCAxLTEgMUgzYTEgMSAwIDAgMS0xLTFWNXpcIi8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8aDUgY2xhc3NOYW1lPVwiZnctYm9sZCBtYi0zXCIgc3R5bGU9e3sgZm9udFNpemU6ICcxLjJyZW0nIH19PlRyYW5zcGFyZW5jeTwvaDU+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkXCIgc3R5bGU9e3sgZm9udFNpemU6ICcwLjlyZW0nLCBsaW5lSGVpZ2h0OiAnMS41JyB9fT5cbiAgICAgICAgICAgICAgICAgICAgV2l0aCBhIHNpbXBsZSAxNSUgY29tbWlzc2lvbiBvbiBzdWNjZXNzZnVsIGRlYWxzLCB3ZSBndWFyYW50ZWUgZmFpcm5lc3NcbiAgICAgICAgICAgICAgICAgICAgYW5kIGNsYXJpdHkgaW4gZXZlcnkgdHJhbnNhY3Rpb24uXG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBGbGV4aWJpbGl0eSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLWxnLTJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkIGgtMTAwIGJvcmRlci0wIHNoYWRvdy1zbSBwb3NpdGlvbi1yZWxhdGl2ZVwiIHN0eWxlPXt7IGJvcmRlclJhZGl1czogJzIwcHgnLCBtaW5IZWlnaHQ6ICczMDBweCcgfX0+XG4gICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicG9zaXRpb24tYWJzb2x1dGUgdG9wLTAgc3RhcnQtMCBoLTEwMFwiXG4gICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzhweCcsXG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJyMyOGE3NDUnLFxuICAgICAgICAgICAgICAgICAgICBib3JkZXJUb3BMZWZ0UmFkaXVzOiAnMjBweCcsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlckJvdHRvbUxlZnRSYWRpdXM6ICcyMHB4J1xuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICA+PC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjYXJkLWJvZHkgcC00IHRleHQtY2VudGVyIGQtZmxleCBmbGV4LWNvbHVtbiBqdXN0aWZ5LWNvbnRlbnQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1zdWNjZXNzIGJnLW9wYWNpdHktMTAgcm91bmRlZC1jaXJjbGUgZC1pbmxpbmUtZmxleCBhbGlnbi1pdGVtcy1jZW50ZXIganVzdGlmeS1jb250ZW50LWNlbnRlclwiIHN0eWxlPXt7IHdpZHRoOiAnNjBweCcsIGhlaWdodDogJzYwcHgnIH19PlxuICAgICAgICAgICAgICAgICAgICAgIDxzdmcgd2lkdGg9XCIzMFwiIGhlaWdodD1cIjMwXCIgZmlsbD1cIiMyOGE3NDVcIiB2aWV3Qm94PVwiMCAwIDE2IDE2XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTggNC43NTRhMy4yNDYgMy4yNDYgMCAxIDAgMCA2LjQ5MiAzLjI0NiAzLjI0NiAwIDAgMCAwLTYuNDkyek01Ljc1NCA4YTIuMjQ2IDIuMjQ2IDAgMSAxIDQuNDkyIDAgMi4yNDYgMi4yNDYgMCAwIDEtNC40OTIgMHpcIi8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTkuNzk2IDEuMzQzYy0uNTI3LTEuNzktMy4wNjUtMS43OS0zLjU5MiAwbC0uMDk0LjMxOWEuODczLjg3MyAwIDAgMS0xLjI1NS41MmwtLjI5Mi0uMTZjLTEuNjQtLjg5Mi0zLjQzMy45MDItMi41NCAyLjU0MWwuMTU5LjI5MmEuODczLjg3MyAwIDAgMS0uNTIgMS4yNTVsLS4zMTkuMDk0Yy0xLjc5LjUyNy0xLjc5IDMuMDY1IDAgMy41OTJsLjMxOS4wOTRhLjg3My44NzMgMCAwIDEgLjUyIDEuMjU1bC0uMTYuMjkyYy0uODkyIDEuNjQuOTAxIDMuNDM0IDIuNTQxIDIuNTRsLjI5Mi0uMTU5YS44NzMuODczIDAgMCAxIDEuMjU1LjUybC4wOTQuMzE5Yy41MjcgMS43OSAzLjA2NSAxLjc5IDMuNTkyIDBsLjA5NC0uMzE5YS44NzMuODczIDAgMCAxIDEuMjU1LS41MmwuMjkyLjE2YzEuNjQuODkzIDMuNDM0LS45MDIgMi41NC0yLjU0MWwtLjE1OS0uMjkyYS44NzMuODczIDAgMCAxIC41Mi0xLjI1NWwuMzE5LS4wOTRjMS43OS0uNTI3IDEuNzktMy4wNjUgMC0zLjU5MmwtLjMxOS0uMDk0YS44NzMuODczIDAgMCAxLS41Mi0xLjI1NWwuMTYtLjI5MmMuODkzLTEuNjQtLjkwMi0zLjQzMy0yLjU0MS0yLjU0bC0uMjkyLjE1OWEuODczLjg3MyAwIDAgMS0xLjI1NS0uNTJsLS4wOTQtLjMxOXpcIi8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8aDUgY2xhc3NOYW1lPVwiZnctYm9sZCBtYi0zXCIgc3R5bGU9e3sgZm9udFNpemU6ICcxLjJyZW0nIH19PkZsZXhpYmlsaXR5PC9oNT5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWRcIiBzdHlsZT17eyBmb250U2l6ZTogJzAuOXJlbScsIGxpbmVIZWlnaHQ6ICcxLjUnIH19PlxuICAgICAgICAgICAgICAgICAgICBXZSBlbWJyYWNlIHRoZSBmdXR1cmUgb2Ygd29yayBhbmQgb2ZmZXIgc2VsbGVycyBhbmRcbiAgICAgICAgICAgICAgICAgICAgY29tcGFuaWVzIGZyZWVkb20gYW5kIGNob2ljZS5cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIEZvY3VzIG9uIHJlc3VsdHMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1sZy0yXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZCBoLTEwMCBib3JkZXItMCBzaGFkb3ctc20gcG9zaXRpb24tcmVsYXRpdmVcIiBzdHlsZT17eyBib3JkZXJSYWRpdXM6ICcyMHB4JywgbWluSGVpZ2h0OiAnMzAwcHgnIH19PlxuICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInBvc2l0aW9uLWFic29sdXRlIHRvcC0wIHN0YXJ0LTAgaC0xMDBcIlxuICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgd2lkdGg6ICc4cHgnLFxuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjMjhhNzQ1JyxcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyVG9wTGVmdFJhZGl1czogJzIwcHgnLFxuICAgICAgICAgICAgICAgICAgICBib3JkZXJCb3R0b21MZWZ0UmFkaXVzOiAnMjBweCdcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgPjwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZC1ib2R5IHAtNCB0ZXh0LWNlbnRlciBkLWZsZXggZmxleC1jb2x1bW4ganVzdGlmeS1jb250ZW50LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc3VjY2VzcyBiZy1vcGFjaXR5LTEwIHJvdW5kZWQtY2lyY2xlIGQtaW5saW5lLWZsZXggYWxpZ24taXRlbXMtY2VudGVyIGp1c3RpZnktY29udGVudC1jZW50ZXJcIiBzdHlsZT17eyB3aWR0aDogJzYwcHgnLCBoZWlnaHQ6ICc2MHB4JyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICA8c3ZnIHdpZHRoPVwiMzBcIiBoZWlnaHQ9XCIzMFwiIGZpbGw9XCIjMjhhNzQ1XCIgdmlld0JveD1cIjAgMCAxNiAxNlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZD1cIk0xNiA4QTggOCAwIDEgMSAwIDhhOCA4IDAgMCAxIDE2IDB6TTguNSA0LjVhLjUuNSAwIDAgMC0xIDB2M2gtM2EuNS41IDAgMCAwIDAgMWgzdjNhLjUuNSAwIDAgMCAxIDB2LTNoM2EuNS41IDAgMCAwIDAtMWgtM3YtM3pcIi8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8aDUgY2xhc3NOYW1lPVwiZnctYm9sZCBtYi0zXCIgc3R5bGU9e3sgZm9udFNpemU6ICcxLjJyZW0nIH19PkZvY3VzIG9uIHJlc3VsdHM8L2g1PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZFwiIHN0eWxlPXt7IGZvbnRTaXplOiAnMC45cmVtJywgbGluZUhlaWdodDogJzEuNScgfX0+XG4gICAgICAgICAgICAgICAgICAgIFdlIGJlbGlldmUgaW4gcmV3YXJkaW5nIHJlc3VsdHMsIG5vdCBlZmZvcnQuIFdlIGNyZWF0ZVxuICAgICAgICAgICAgICAgICAgICBhIHBlcmZvcm1hbmNlLW9yaWVudGVkIGN1bHR1cmUgZm9yIGV2ZXJ5b25lXG4gICAgICAgICAgICAgICAgICAgIGludm9sdmVkLlxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgey8qIEFzc2lnbm1lbnRzIFNlY3Rpb24gKi99XG4gICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS01IGJnLWxpZ2h0XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyXCI+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cImZ3LWJvbGQgbWItNFwiPkFzc2lnbm1lbnRzPC9oMj5cbiAgICAgICAgICA8aDUgY2xhc3NOYW1lPVwidGV4dC1tdXRlZCBtYi00XCI+TGF0ZXN0IEFzc2lnbm1lbnRzPC9oNT5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicm93XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1sZy00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZCBib3JkZXItMCBzaGFkb3ctc21cIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNhcmQtaW1nLXRvcCBiZy1wcmltYXJ5IGQtZmxleCBhbGlnbi1pdGVtcy1jZW50ZXIganVzdGlmeS1jb250ZW50LWNlbnRlclwiIHN0eWxlPXt7IGhlaWdodDogJzIwMHB4JyB9fT5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICA8c3ZnIHdpZHRoPVwiNjBcIiBoZWlnaHQ9XCI2MFwiIGZpbGw9XCJ3aGl0ZVwiIHZpZXdCb3g9XCIwIDAgMTYgMTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTAgMS4xNDZDMCAuNTEzLjUyNiAwIDEuMTc1IDBoMTMuNjVDMTUuNDc0IDAgMTYgLjUxMyAxNiAxLjE0NnYxMy43MDhjMCAuNjMzLS41MjYgMS4xNDYtMS4xNzUgMS4xNDZIMS4xNzVDLjUyNiAxNiAwIDE1LjQ4NyAwIDE0Ljg1NFYxLjE0NnpNMS4xNzUgMWEuMTQ2LjE0NiAwIDAgMC0uMTQ2LjE0NnYxMy43MDhjMCAuMDguMDY2LjE0Ni4xNDYuMTQ2aDEzLjY1YS4xNDYuMTQ2IDAgMCAwIC4xNDYtLjE0NlYxLjE0NmEuMTQ2LjE0NiAwIDAgMC0uMTQ2LS4xNDZIMS4xNzV6XCIvPlxuICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNOCA1LjVhMi41IDIuNSAwIDEgMCAwIDUgMi41IDIuNSAwIDAgMCAwLTV6TTQuNSA4YTMuNSAzLjUgMCAxIDEgNyAwIDMuNSAzLjUgMCAwIDEtNyAwelwiLz5cbiAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgIDxoNiBjbGFzc05hbWU9XCJtdC0yIG1iLTBcIj5JbmZvcm1hdGlvbiBUZWNobm9sb2d5PC9oNj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2FyZC1ib2R5XCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJiYWRnZSBiZy1wcmltYXJ5IG1iLTJcIj5JbmZvcm1hdGlvbiBUZWNobm9sb2d5PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zdWNjZXNzIGZzLTQgZnctYm9sZFwiPiQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8aDUgY2xhc3NOYW1lPVwiZnctYm9sZCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvYXNzaWdubWVudHMvNVwiIGNsYXNzTmFtZT1cInRleHQtZGVjb3JhdGlvbi1ub25lIHRleHQtZGFya1wiPlxuICAgICAgICAgICAgICAgICAgICAgIFdlYiBhcHBsaWNhdGlvbiBkZXZlbG9wbWVudFxuICAgICAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgICAgICA8L2g1PlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZCBtYi0yXCI+VGVzdCBBc3NpZ25tZW50PC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibWItMlwiPjxzdHJvbmc+UGF5bWVudCBUeXBlOjwvc3Ryb25nPiBIb3VybHkgKCQxNy4wMCk8L3A+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtYi0zXCI+QXBwbGljYXRpb25zOiAxPC9wPlxuICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9sb2dpblwiIGNsYXNzTmFtZT1cImJ0biBidG4tc3VjY2VzcyB3LTEwMFwiPlxuICAgICAgICAgICAgICAgICAgICBUYWtlIFRoaXMgRGVhbFxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgey8qIEZvb3RlciAtIEV4YWN0IG1hdGNoIHRvIHlvdXIgZGVzaWduICovfVxuICAgICAgPGZvb3RlciBjbGFzc05hbWU9XCJiZy13aGl0ZSBweS01IGJvcmRlci10b3BcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJvd1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtbGctM1wiPlxuICAgICAgICAgICAgICA8aDUgY2xhc3NOYW1lPVwiZnctYm9sZCB0ZXh0LXN1Y2Nlc3MgbWItNFwiPkRlYWxDbG9zZWQ8L2g1PlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkIG1iLTJcIj5BbXN0ZXJkYW0sIFRoZSBOZXRoZXJsYW5kczwvcD5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZCBtYi00XCI+PHN0cm9uZz5FbWFpbDo8L3N0cm9uZz4gaW5mb0BkZWFsY2xvc2VkLmNvbTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1sZy0zXCI+XG4gICAgICAgICAgICAgIDxoNiBjbGFzc05hbWU9XCJmdy1ib2xkIG1iLTMgdGV4dC1zdWNjZXNzXCI+VXNlZnVsIGxpbmtzPC9oNj5cbiAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cImxpc3QtdW5zdHlsZWRcIj5cbiAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwibWItMlwiPjxMaW5rIGhyZWY9XCIvXCIgY2xhc3NOYW1lPVwidGV4dC1tdXRlZCB0ZXh0LWRlY29yYXRpb24tbm9uZVwiPkhvbWU8L0xpbms+PC9saT5cbiAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwibWItMlwiPjxMaW5rIGhyZWY9XCIvYWJvdXRcIiBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkIHRleHQtZGVjb3JhdGlvbi1ub25lXCI+QWJvdXQgdXM8L0xpbms+PC9saT5cbiAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwibWItMlwiPjxMaW5rIGhyZWY9XCIvYXNzaWdubWVudHNcIiBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkIHRleHQtZGVjb3JhdGlvbi1ub25lXCI+QXNzaWdubWVudHM8L0xpbms+PC9saT5cbiAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1sZy0zXCI+XG4gICAgICAgICAgICAgIDxoNiBjbGFzc05hbWU9XCJmdy1ib2xkIG1iLTMgdGV4dC1zdWNjZXNzXCI+T3RoZXIgbGlua3M8L2g2PlxuICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwibGlzdC11bnN0eWxlZFwiPlxuICAgICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJtYi0yXCI+PExpbmsgaHJlZj1cIi9jb250YWN0XCIgY2xhc3NOYW1lPVwidGV4dC1tdXRlZCB0ZXh0LWRlY29yYXRpb24tbm9uZVwiPkNvbnRhY3QgVXM8L0xpbms+PC9saT5cbiAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwibWItMlwiPjxMaW5rIGhyZWY9XCIvdGVybXNcIiBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkIHRleHQtZGVjb3JhdGlvbi1ub25lXCI+VGVybXMgb2Ygc2VydmljZTwvTGluaz48L2xpPlxuICAgICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJtYi0yXCI+PExpbmsgaHJlZj1cIi9wcml2YWN5XCIgY2xhc3NOYW1lPVwidGV4dC1tdXRlZCB0ZXh0LWRlY29yYXRpb24tbm9uZVwiPlByaXZhY3kgcG9saWN5PC9MaW5rPjwvbGk+XG4gICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtbGctM1wiPlxuICAgICAgICAgICAgICA8aDYgY2xhc3NOYW1lPVwiZnctYm9sZCBtYi0zIHRleHQtc3VjY2Vzc1wiPkZvbGxvdyB1czwvaDY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZC1mbGV4IGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgPGEgaHJlZj1cIiNcIiBjbGFzc05hbWU9XCJidG4gYnRuLW91dGxpbmUtc2Vjb25kYXJ5IGJ0bi1zbSByb3VuZGVkLWNpcmNsZVwiPlxuICAgICAgICAgICAgICAgICAgPHN2ZyB3aWR0aD1cIjE2XCIgaGVpZ2h0PVwiMTZcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAxNiAxNlwiPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTUuMDI2IDE1YzYuMDM4IDAgOS4zNDEtNS4wMDMgOS4zNDEtOS4zMzQgMC0uMTQgMC0uMjgyLS4wMDYtLjQyMkE2LjY4NSA2LjY4NSAwIDAgMCAxNiAzLjU0MmE2LjY1OCA2LjY1OCAwIDAgMS0xLjg4OS41MTggMy4zMDEgMy4zMDEgMCAwIDAgMS40NDctMS44MTcgNi41MzMgNi41MzMgMCAwIDEtMi4wODcuNzkzQTMuMjg2IDMuMjg2IDAgMCAwIDcuODc1IDYuMDNhOS4zMjUgOS4zMjUgMCAwIDEtNi43NjctMy40MjkgMy4yODkgMy4yODkgMCAwIDAgMS4wMTggNC4zODJBMy4zMjMgMy4zMjMgMCAwIDEgLjY0IDYuNTc1di4wNDVhMy4yODggMy4yODggMCAwIDAgMi42MzIgMy4yMTggMy4yMDMgMy4yMDMgMCAwIDEtLjg2NS4xMTUgMy4yMyAzLjIzIDAgMCAxLS42MTQtLjA1NyAzLjI4MyAzLjI4MyAwIDAgMCAzLjA2NyAyLjI3N0E2LjU4OCA2LjU4OCAwIDAgMSAuNzggMTMuNThhNi4zMiA2LjMyIDAgMCAxLS43OC0uMDQ1QTkuMzQ0IDkuMzQ0IDAgMCAwIDUuMDI2IDE1elwiLz5cbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgICA8YSBocmVmPVwiI1wiIGNsYXNzTmFtZT1cImJ0biBidG4tb3V0bGluZS1zZWNvbmRhcnkgYnRuLXNtIHJvdW5kZWQtY2lyY2xlXCI+XG4gICAgICAgICAgICAgICAgICA8c3ZnIHdpZHRoPVwiMTZcIiBoZWlnaHQ9XCIxNlwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDE2IDE2XCI+XG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNMTYgOC4wNDljMC00LjQ0Ni0zLjU4Mi04LjA1LTgtOC4wNUMzLjU4IDAtLjAwMiAzLjYwMy0uMDAyIDguMDVjMCA0LjAxNyAyLjkyNiA3LjM0NyA2Ljc1IDcuOTUxdi01LjYyNWgtMi4wM1Y4LjA1SDYuNzVWNi4yNzVjMC0yLjAxNyAxLjE5NS0zLjEzMSAzLjAyMi0zLjEzMS44NzYgMCAxLjc5MS4xNTcgMS43OTEuMTU3djEuOThoLTEuMDA5Yy0uOTkzIDAtMS4zMDMuNjIxLTEuMzAzIDEuMjU4djEuNTFoMi4yMThsLS4zNTQgMi4zMjZIOS4yNVYxNmMzLjgyNC0uNjA0IDYuNzUtMy45MzQgNi43NS03Ljk1MXpcIi8+XG4gICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICAgPGEgaHJlZj1cIiNcIiBjbGFzc05hbWU9XCJidG4gYnRuLW91dGxpbmUtc2Vjb25kYXJ5IGJ0bi1zbSByb3VuZGVkLWNpcmNsZVwiPlxuICAgICAgICAgICAgICAgICAgPHN2ZyB3aWR0aD1cIjE2XCIgaGVpZ2h0PVwiMTZcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAxNiAxNlwiPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTggMEM1LjgyOSAwIDUuNTU2LjAxIDQuNzAzLjA0OCAzLjg1LjA4OCAzLjI2OS4yMjIgMi43Ni40MmEzLjkxNyAzLjkxNyAwIDAgMC0xLjQxNy45MjNBMy45MjcgMy45MjcgMCAwIDAgLjQyIDIuNzZDLjIyMiAzLjI2OC4wODcgMy44NS4wNDggNC43LjAxIDUuNTU1IDAgNS44MjcgMCA4LjAwMWMwIDIuMTcyLjAxIDIuNDQ0LjA0OCAzLjI5Ny4wNC44NTIuMTc0IDEuNDMzLjM3MiAxLjk0Mi4yMDUuNTI2LjQ3OC45NzIuOTIzIDEuNDE3LjQ0NC40NDUuODkuNzE5IDEuNDE2LjkyMy41MS4xOTggMS4wOS4zMzMgMS45NDIuMzcyQzUuNTU1IDE1Ljk5IDUuODI3IDE2IDggMTZzMi40NDQtLjAxIDMuMjk4LS4wNDhjLjg1MS0uMDQgMS40MzQtLjE3NCAxLjk0My0uMzcyYTMuOTE2IDMuOTE2IDAgMCAwIDEuNDE2LS45MjNjLjQ0NS0uNDQ1LjcxOC0uODkxLjkyMy0xLjQxNy4xOTctLjUwOS4zMzItMS4wOS4zNzItMS45NDJDMTUuOTkgMTAuNDQ1IDE2IDEwLjE3MyAxNiA4cy0uMDEtMi40NDUtLjA0OC0zLjI5OWMtLjA0LS44NTEtLjE3NS0xLjQzMy0uMzcyLTEuOTQxYTMuOTI2IDMuOTI2IDAgMCAwLS45MjMtMS40MTdBMy45MTEgMy45MTEgMCAwIDAgMTMuMjQuNDJjLS41MS0uMTk4LTEuMDkyLS4zMzMtMS45NDMtLjM3MkMxMC40NDMuMDEgMTAuMTcyIDAgNy45OTggMGguMDAzem0tLjcxNyAxLjQ0MmguNzE4YzIuMTM2IDAgMi4zODkuMDA3IDMuMjMyLjA0Ni43OC4wMzUgMS4yMDQuMTY2IDEuNDg2LjI3NS4zNzMuMTQ1LjY0LjMxOS45Mi41OTkuMjguMjguNDUzLjU0Ni41OTguOTIuMTEuMjgxLjI0LjcwNS4yNzUgMS40ODUuMDM5Ljg0My4wNDcgMS4wOTYuMDQ3IDMuMjMxcy0uMDA4IDIuMzg5LS4wNDcgMy4yMzJjLS4wMzUuNzgtLjE2NiAxLjIwMy0uMjc1IDEuNDg1YTIuNDcgMi40NyAwIDAgMS0uNTk5LjkxOWMtLjI4LjI4LS41NDYuNDUzLS45Mi41OTgtLjI4LjExLS43MDQuMjQtMS40ODUuMjc2LS44NDMuMDM4LTEuMDk2LjA0Ny0zLjIzMi4wNDdzLTIuMzktLjAwOS0zLjIzMy0uMDQ3Yy0uNzgtLjAzNi0xLjIwMy0uMTY2LTEuNDg1LS4yNzZhMi40NzggMi40NzggMCAwIDEtLjkyLS41OTggMi40OCAyLjQ4IDAgMCAxLS42LS45MmMtLjEwOS0uMjgxLS4yNC0uNzA1LS4yNzUtMS40ODUtLjAzOC0uODQzLS4wNDYtMS4wOTYtLjA0Ni0zLjIzMyAwLTIuMTM2LjAwOC0yLjM4OC4wNDYtMy4yMzEuMDM2LS43OC4xNjYtMS4yMDQuMjc2LTEuNDg2LjE0NS0uMzczLjMxOS0uNjQuNTk5LS45Mi4yOC0uMjguNTQ2LS40NTMuOTItLjU5OC4yODItLjExLjcwNS0uMjQgMS40ODUtLjI3Ni43MzgtLjAzNCAxLjAyNC0uMDQ0IDIuNTE1LS4wNDV2LjAwMnptNC45ODggMS4zMjhhLjk2Ljk2IDAgMSAwIDAgMS45Mi45Ni45NiAwIDAgMCAwLTEuOTJ6bS00LjI3IDEuMTIyYTQuMTA5IDQuMTA5IDAgMSAwIDAgOC4yMTcgNC4xMDkgNC4xMDkgMCAwIDAgMC04LjIxN3ptMCAxLjQ0MWEyLjY2NyAyLjY2NyAwIDEgMSAwIDUuMzM0IDIuNjY3IDIuNjY3IDAgMCAxIDAtNS4zMzR6XCIvPlxuICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwiYnRuIGJ0bi1vdXRsaW5lLXNlY29uZGFyeSBidG4tc20gcm91bmRlZC1jaXJjbGVcIj5cbiAgICAgICAgICAgICAgICAgIDxzdmcgd2lkdGg9XCIxNlwiIGhlaWdodD1cIjE2XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMTYgMTZcIj5cbiAgICAgICAgICAgICAgICAgICAgPHBhdGggZD1cIk0wIDEuMTQ2QzAgLjUxMy41MjYgMCAxLjE3NSAwaDEzLjY1QzE1LjQ3NCAwIDE2IC41MTMgMTYgMS4xNDZ2MTMuNzA4YzAgLjYzMy0uNTI2IDEuMTQ2LTEuMTc1IDEuMTQ2SDEuMTc1Qy41MjYgMTYgMCAxNS40ODcgMCAxNC44NTRWMS4xNDZ6bTQuOTQzIDEyLjI0OFY2LjE2OUgyLjU0MnY3LjIyNWgyLjQwMXptLTEuMi04LjIxMmMuODM3IDAgMS4zNTgtLjU1NCAxLjM1OC0xLjI0OC0uMDE1LS43MDktLjUyLTEuMjQ4LTEuMzQyLTEuMjQ4LS44MjIgMC0xLjM1OS41NC0xLjM1OSAxLjI0OCAwIC42OTQuNTIxIDEuMjQ4IDEuMzI3IDEuMjQ4aC4wMTZ6bTQuOTA4IDguMjEyVjkuMzU5YzAtLjIxNi4wMTYtLjQzMi4wOC0uNTg2LjE3My0uNDMxLjU2OC0uODc4IDEuMjMyLS44NzguODY5IDAgMS4yMTYuNjYyIDEuMjE2IDEuNjM0djMuODY1aDIuNDAxVjkuMjVjMC0yLjIyLTEuMTg0LTMuMjUyLTIuNzY0LTMuMjUyLTEuMjc0IDAtMS44NDUuNy0yLjE2NSAxLjE5M3YuMDI1aC0uMDE2YTUuNTQgNS41NCAwIDAgMSAuMDE2LS4wMjVWNi4xNjloLTIuNGMuMDMuNjc4IDAgNy4yMjUgMCA3LjIyNWgyLjR6XCIvPlxuICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGhyIGNsYXNzTmFtZT1cIm15LTRcIiAvPlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyb3cgYWxpZ24taXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1tZC02XCI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQgbWItMFwiPsKpIENvcHlyaWdodCA8c3Ryb25nPkRlYWxDbG9zZWQsPC9zdHJvbmc+IEFsbCBSaWdodHMgUmVzZXJ2ZWQ8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLW1kLTYgdGV4dC1lbmRcIj5cbiAgICAgICAgICAgICAgPGltZyBzcmM9XCIvdXMucG5nXCIgYWx0PVwiRW5nbGlzaFwiIHdpZHRoPVwiMjBcIiBjbGFzc05hbWU9XCJtZS0yXCIgLz5cbiAgICAgICAgICAgICAgPGltZyBzcmM9XCIvbmwucG5nXCIgYWx0PVwiRHV0Y2hcIiB3aWR0aD1cIjIwXCIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZm9vdGVyPlxuICAgIDwvPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgSG9tZVBhZ2U7XG4iXSwibmFtZXMiOlsiSGVhZCIsIkxpbmsiLCJSZWFjdCIsIkZpQnJpZWZjYXNlIiwiRmlVc2VycyIsIkhvbWVQYWdlIiwidGl0bGUiLCJtZXRhIiwibmFtZSIsImNvbnRlbnQiLCJsaW5rIiwicmVsIiwiaHJlZiIsIm5hdiIsImNsYXNzTmFtZSIsImRpdiIsInN2ZyIsIndpZHRoIiwiaGVpZ2h0Iiwidmlld0JveCIsImZpbGwiLCJjaXJjbGUiLCJjeCIsImN5IiwiciIsInBhdGgiLCJkIiwic3Ryb2tlIiwic3Ryb2tlV2lkdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzcGFuIiwic3R5bGUiLCJmb250U2l6ZSIsImJ1dHRvbiIsInR5cGUiLCJkYXRhLWJzLXRvZ2dsZSIsImRhdGEtYnMtdGFyZ2V0IiwiaWQiLCJ1bCIsImxpIiwiZm9udFdlaWdodCIsInNlY3Rpb24iLCJiYWNrZ3JvdW5kSW1hZ2UiLCJiYWNrZ3JvdW5kU2l6ZSIsImJhY2tncm91bmRQb3NpdGlvbiIsIm1pbkhlaWdodCIsIm1hcmdpblRvcCIsImgxIiwibGluZUhlaWdodCIsImJyIiwicCIsIm9wYWNpdHkiLCJtYXhXaWR0aCIsImJhY2tncm91bmRDb2xvciIsImJvcmRlciIsImNvbG9yIiwidHJhbnNpdGlvbiIsIm9uTW91c2VFbnRlciIsImUiLCJ0YXJnZXQiLCJib3JkZXJDb2xvciIsIm9uTW91c2VMZWF2ZSIsIm9uQ2xpY2siLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImg2IiwiaDIiLCJzdHJvbmciLCJmb250U3R5bGUiLCJib3JkZXJSYWRpdXMiLCJib3JkZXJUb3BMZWZ0UmFkaXVzIiwiYm9yZGVyQm90dG9tTGVmdFJhZGl1cyIsInBhZGRpbmdMZWZ0IiwiZmlsbFJ1bGUiLCJoNCIsImg1IiwibWFyZ2luIiwiYmFja2dyb3VuZFJlcGVhdCIsInNpemUiLCJoMyIsImZvb3RlciIsImEiLCJociIsImltZyIsInNyYyIsImFsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n"));

/***/ })

});