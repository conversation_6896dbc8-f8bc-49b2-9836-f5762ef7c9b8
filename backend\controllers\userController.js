const { validationResult } = require('express-validator');
const User = require('../models/User');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const { uploadProfileImage: uploadToCloudinary, deleteImage } = require('../utils/cloudinary');

// Configure multer for file uploads (using memory storage for Cloudinary)
const storage = multer.memoryStorage();

const fileFilter = (req, file, cb) => {
  const allowedTypes = process.env.ALLOWED_IMAGE_TYPES.split(',');
  const fileExt = path.extname(file.originalname).toLowerCase().slice(1);
  
  if (allowedTypes.includes(fileExt)) {
    cb(null, true);
  } else {
    cb(new Error(`Invalid file type. Allowed types: ${allowedTypes.join(', ')}`), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.UPLOAD_MAX_SIZE) * 1024 // Convert KB to bytes
  }
});

// Get user profile
const getProfile = async (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        user: req.user.getPublicProfile()
      }
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get profile'
    });
  }
};

// Update user profile
const updateProfile = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const allowedFields = [
      'first_name', 'last_name', 'company_name', 'bio', 'phone', 
      'address', 'website', 'kvk_number', 'vat_number', 'experience_years'
    ];

    const updateData = {};
    allowedFields.forEach(field => {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    });

    // Handle skills array for freelancers
    if (req.body.skills && req.user.role === 'freelancer') {
      updateData.skills = Array.isArray(req.body.skills) ? req.body.skills : [];
    }

    await req.user.update(updateData);

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: {
        user: req.user.getPublicProfile()
      }
    });

  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update profile'
    });
  }
};

// Upload profile image to Cloudinary
const uploadProfileImage = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded'
      });
    }

    // Create a temporary file from buffer for Cloudinary upload
    const tempFilePath = path.join(__dirname, '..', 'temp', `temp_${Date.now()}_${req.file.originalname}`);

    // Ensure temp directory exists
    const tempDir = path.dirname(tempFilePath);
    await fs.mkdir(tempDir, { recursive: true });

    // Write buffer to temporary file
    await fs.writeFile(tempFilePath, req.file.buffer);

    // Delete old profile image from Cloudinary if it exists
    if (req.user.profile_image && req.user.profile_image !== 'default.png' && req.user.profile_image.includes('cloudinary')) {
      // Extract public ID from Cloudinary URL
      const publicIdMatch = req.user.profile_image.match(/\/([^\/]+)\.[^.]+$/);
      if (publicIdMatch) {
        await deleteImage(`dealclosed/profiles/${publicIdMatch[1]}`);
      }
    }

    // Upload to Cloudinary
    const uploadResult = await uploadToCloudinary(tempFilePath, req.user.id);

    // Clean up temporary file
    try {
      await fs.unlink(tempFilePath);
    } catch (error) {
      console.log('Temp file cleanup failed:', error.message);
    }

    if (!uploadResult.success) {
      return res.status(500).json({
        success: false,
        message: 'Failed to upload image to cloud storage'
      });
    }

    // Update user with new Cloudinary URL
    await req.user.update({ profile_image: uploadResult.url });

    res.json({
      success: true,
      message: 'Profile image uploaded successfully',
      data: {
        profile_image: uploadResult.url,
        image_url: uploadResult.url
      }
    });

  } catch (error) {
    console.error('Upload profile image error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload profile image'
    });
  }
};

// Delete account
const deleteAccount = async (req, res) => {
  try {
    // Soft delete - just deactivate the account
    await req.user.update({ is_active: false });

    res.json({
      success: true,
      message: 'Account deactivated successfully'
    });

  } catch (error) {
    console.error('Delete account error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete account'
    });
  }
};

module.exports = {
  getProfile,
  updateProfile,
  uploadProfileImage: [upload.single('profile_image'), uploadProfileImage],
  deleteAccount
};
