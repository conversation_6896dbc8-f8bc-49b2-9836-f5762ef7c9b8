/* Global Styles */
:root {
  --bs-primary: #007bff;
  --bs-primary-rgb: 0, 123, 255;
  --bs-secondary: #6c757d;
  --bs-success: #28a745;
  --bs-info: #17a2b8;
  --bs-warning: #ffc107;
  --bs-danger: #dc3545;
  --bs-light: #f8f9fa;
  --bs-dark: #343a40;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  background-color: #f8f9fa;
}

a {
  color: inherit;
  text-decoration: none;
}

/* Custom utility classes */
.min-vh-50 {
  min-height: 50vh;
}

.min-vh-100 {
  min-height: 100vh;
}

/* Custom button styles */
.btn {
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Card styles */
.card {
  border-radius: 1rem;
  transition: all 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Form styles */
.form-control {
  border-radius: 0.5rem;
  border: 1px solid #dee2e6;
  transition: all 0.2s ease-in-out;
}

.form-control:focus {
  border-color: var(--bs-primary);
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Navbar styles */
.navbar {
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.95) !important;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
}

/* Remove focus outline from navbar links */
.navbar .nav-link:focus,
.navbar .navbar-brand:focus,
.navbar .btn:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* Navbar hover effects */
.navbar .nav-link:hover {
  color: #28a745 !important;
}

.navbar .nav-link.text-success {
  color: #28a745 !important;
}

/* Loading spinner */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Profile image styles */
.profile-image {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Alert styles */
.alert {
  border-radius: 0.75rem;
  border: none;
}

/* Badge styles */
.badge {
  font-weight: 500;
  padding: 0.5em 0.75em;
}

/* Footer styles */
footer {
  margin-top: auto;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive utilities */
@media (max-width: 768px) {
  .display-4 {
    font-size: 2.5rem;
  }
  
  .lead {
    font-size: 1.1rem;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Focus styles for accessibility */
.btn:focus,
.form-control:focus,
.form-select:focus {
  outline: none;
}

/* Print styles */
@media print {
  .navbar,
  footer,
  .btn {
    display: none !important;
  }
}
