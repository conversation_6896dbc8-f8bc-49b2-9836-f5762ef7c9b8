{"name": "dealclosed-backend", "version": "1.0.0", "description": "Backend API for DealClosed Partner platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"bcryptjs": "^2.4.3", "cloudinary": "^2.7.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.8", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.35.2", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "keywords": ["sales", "platform", "authentication", "api"], "author": "DealClosed Partner", "license": "MIT"}