require('dotenv').config();
const { sendVerificationEmail } = require('./utils/email');

async function testEmail() {
  console.log('🧪 Testing Email Configuration...\n');

  console.log('📧 SMTP Settings:');
  console.log(`Host: ${process.env.SMTP_HOST}`);
  console.log(`Port: ${process.env.SMTP_PORT}`);
  console.log(`Secure: ${process.env.SMTP_SECURE}`);
  console.log(`Username: ${process.env.SMTP_USERNAME}`);
  console.log(`From: ${process.env.SMTP_FROM_EMAIL}\n`);

  try {
    console.log('📤 Sending test verification email to your actual email...');
    // Test with a real email - replace with your email
    await sendVerificationEmail(
      '<EMAIL>', // This will test the SMTP connection
      'test-token-123',
      'Test User'
    );
    console.log('✅ Email sent successfully!');
    console.log('📬 Check your email inbox for the verification email.');
  } catch (error) {
    console.error('❌ Email failed:', error.message);
    console.error('Full error:', error);

    // Additional debugging
    if (error.code === 'EAUTH') {
      console.error('🔐 Authentication failed - check SMTP username/password');
    } else if (error.code === 'ECONNECTION') {
      console.error('🌐 Connection failed - check SMTP host/port');
    }
  }
}

testEmail();
